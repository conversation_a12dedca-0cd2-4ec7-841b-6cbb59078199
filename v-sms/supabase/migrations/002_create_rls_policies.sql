-- Row Level Security (RLS) Policies for Multi-Tenant School Management System
-- This ensures complete data isolation between schools

-- Enable RLS on all tables
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE parents ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_parents ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE assignment_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE examinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE exam_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE results ENABLE ROW LEVEL SECURITY;
ALTER TABLE fee_structures ENABLE ROW LEVEL SECURITY;
ALTER TABLE fee_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE timetable ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's school_id
CREATE OR REPLACE FUNCTION get_current_user_school_id()
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT school_id 
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT role = 'super_admin'
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schools table policies
CREATE POLICY "Super admins can view all schools" ON schools
  FOR SELECT USING (is_super_admin());

CREATE POLICY "Users can view their own school" ON schools
  FOR SELECT USING (id = get_current_user_school_id());

CREATE POLICY "Super admins can insert schools" ON schools
  FOR INSERT WITH CHECK (is_super_admin());

CREATE POLICY "Super admins can update schools" ON schools
  FOR UPDATE USING (is_super_admin());

CREATE POLICY "Super admins can delete schools" ON schools
  FOR DELETE USING (is_super_admin());

-- Profiles table policies
CREATE POLICY "Users can view profiles in their school" ON profiles
  FOR SELECT USING (
    school_id = get_current_user_school_id() OR is_super_admin()
  );

CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can insert profiles in their school" ON profiles
  FOR INSERT WITH CHECK (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

CREATE POLICY "Admins can update profiles in their school" ON profiles
  FOR UPDATE USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

CREATE POLICY "Admins can delete profiles in their school" ON profiles
  FOR DELETE USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Departments table policies
CREATE POLICY "Users can view departments in their school" ON departments
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins can manage departments in their school" ON departments
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Subjects table policies
CREATE POLICY "Users can view subjects in their school" ON subjects
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins and teachers can manage subjects in their school" ON subjects
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Classes table policies
CREATE POLICY "Users can view classes in their school" ON classes
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins can manage classes in their school" ON classes
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Students table policies
CREATE POLICY "Users can view students in their school" ON students
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Students can view their own record" ON students
  FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Parents can view their children's records" ON students
  FOR SELECT USING (
    id IN (
      SELECT sp.student_id 
      FROM student_parents sp 
      JOIN parents p ON sp.parent_id = p.id 
      WHERE p.profile_id = auth.uid()
    )
  );

CREATE POLICY "Admins and teachers can manage students in their school" ON students
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Teachers table policies
CREATE POLICY "Users can view teachers in their school" ON teachers
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Teachers can view their own record" ON teachers
  FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Admins can manage teachers in their school" ON teachers
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Parents table policies
CREATE POLICY "Users can view parents in their school" ON parents
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Parents can view their own record" ON parents
  FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Admins can manage parents in their school" ON parents
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Student-Parents relationship policies
CREATE POLICY "Users can view student-parent relationships in their school" ON student_parents
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins can manage student-parent relationships in their school" ON student_parents
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Assignments table policies
CREATE POLICY "Users can view assignments in their school" ON assignments
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Teachers can manage assignments in their school" ON assignments
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Assignment submissions policies
CREATE POLICY "Users can view assignment submissions in their school" ON assignment_submissions
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Students can manage their own submissions" ON assignment_submissions
  FOR ALL USING (
    student_id IN (SELECT id FROM students WHERE profile_id = auth.uid())
  );

CREATE POLICY "Teachers can manage submissions in their school" ON assignment_submissions
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Attendance policies
CREATE POLICY "Users can view attendance in their school" ON attendance
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Students can view their own attendance" ON attendance
  FOR SELECT USING (
    student_id IN (SELECT id FROM students WHERE profile_id = auth.uid())
  );

CREATE POLICY "Parents can view their children's attendance" ON attendance
  FOR SELECT USING (
    student_id IN (
      SELECT sp.student_id
      FROM student_parents sp
      JOIN parents p ON sp.parent_id = p.id
      WHERE p.profile_id = auth.uid()
    )
  );

CREATE POLICY "Teachers can manage attendance in their school" ON attendance
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Examinations policies
CREATE POLICY "Users can view examinations in their school" ON examinations
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins and teachers can manage examinations in their school" ON examinations
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Exam subjects policies
CREATE POLICY "Users can view exam subjects in their school" ON exam_subjects
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins and teachers can manage exam subjects in their school" ON exam_subjects
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Results policies
CREATE POLICY "Users can view results in their school" ON results
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Students can view their own results" ON results
  FOR SELECT USING (
    student_id IN (SELECT id FROM students WHERE profile_id = auth.uid())
  );

CREATE POLICY "Parents can view their children's results" ON results
  FOR SELECT USING (
    student_id IN (
      SELECT sp.student_id
      FROM student_parents sp
      JOIN parents p ON sp.parent_id = p.id
      WHERE p.profile_id = auth.uid()
    )
  );

CREATE POLICY "Teachers can manage results in their school" ON results
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Fee structures policies
CREATE POLICY "Users can view fee structures in their school" ON fee_structures
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins can manage fee structures in their school" ON fee_structures
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Fee payments policies
CREATE POLICY "Users can view fee payments in their school" ON fee_payments
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Students can view their own fee payments" ON fee_payments
  FOR SELECT USING (
    student_id IN (SELECT id FROM students WHERE profile_id = auth.uid())
  );

CREATE POLICY "Parents can view their children's fee payments" ON fee_payments
  FOR SELECT USING (
    student_id IN (
      SELECT sp.student_id
      FROM student_parents sp
      JOIN parents p ON sp.parent_id = p.id
      WHERE p.profile_id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage fee payments in their school" ON fee_payments
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'super_admin'))
    OR is_super_admin()
  );

-- Timetable policies
CREATE POLICY "Users can view timetable in their school" ON timetable
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins and teachers can manage timetable in their school" ON timetable
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Notifications policies
CREATE POLICY "Users can view notifications in their school" ON notifications
  FOR SELECT USING (
    school_id = get_current_user_school_id() OR
    target_user_id = auth.uid() OR
    is_super_admin()
  );

CREATE POLICY "Admins and teachers can manage notifications in their school" ON notifications
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );

-- Communications policies
CREATE POLICY "Users can view communications in their school" ON communications
  FOR SELECT USING (
    school_id = get_current_user_school_id() OR
    sender_id = auth.uid() OR
    recipient_id = auth.uid() OR
    is_super_admin()
  );

CREATE POLICY "Users can send communications in their school" ON communications
  FOR INSERT WITH CHECK (
    school_id = get_current_user_school_id() AND sender_id = auth.uid()
  );

CREATE POLICY "Users can update their own communications" ON communications
  FOR UPDATE USING (sender_id = auth.uid());

-- Events policies
CREATE POLICY "Users can view events in their school" ON events
  FOR SELECT USING (school_id = get_current_user_school_id() OR is_super_admin());

CREATE POLICY "Admins and teachers can manage events in their school" ON events
  FOR ALL USING (
    (school_id = get_current_user_school_id() AND get_current_user_role() IN ('admin', 'teacher', 'super_admin'))
    OR is_super_admin()
  );
