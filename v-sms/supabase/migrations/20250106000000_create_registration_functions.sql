-- <PERSON>reate functions to handle student and teacher registration that bypass RLS
-- These functions run with elevated privileges to create records during signup

-- Function to register a new student
CREATE OR REPLACE FUNCTION register_student(
  student_data jsonb,
  school_id uuid
) RETURNS uuid
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  new_student_id uuid;
BEGIN
  -- Insert student record with all required fields
  INSERT INTO students (
    school_id,
    student_id,
    first_name,
    last_name,
    parent_email,
    phone,
    address,
    date_of_birth,
    gender,
    class_id,
    admission_date,
    parent_contact,
    emergency_contact,
    medical_info,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    school_id,
    student_data->>'studentId',
    student_data->>'firstName',
    student_data->>'lastName',
    student_data->>'email',
    student_data->>'phone',
    student_data->>'address',
    CASE 
      WHEN student_data->>'dateOfBirth' IS NOT NULL 
      THEN (student_data->>'dateOfBirth')::date 
      ELSE NULL 
    END,
    student_data->>'gender',
    CASE 
      WHEN student_data->>'classId' IS NOT NULL AND student_data->>'classId' != 'null'
      THEN (student_data->>'classId')::uuid 
      ELSE NULL 
    END,
    CASE 
      WHEN student_data->>'admissionDate' IS NOT NULL 
      THEN (student_data->>'admissionDate')::date 
      ELSE CURRENT_DATE 
    END,
    student_data->>'parentContact',
    student_data->>'emergencyContact',
    student_data->>'medicalInfo',
    true,
    NOW(),
    NOW()
  ) RETURNING id INTO new_student_id;
  
  RETURN new_student_id;
END;
$$;

-- Function to register a new teacher
CREATE OR REPLACE FUNCTION register_teacher(
  teacher_data jsonb,
  school_id uuid
) RETURNS uuid
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  new_teacher_id uuid;
BEGIN
  -- Insert teacher record with all required fields
  INSERT INTO teachers (
    school_id,
    employee_id,
    first_name,
    last_name,
    email,
    phone,
    hire_date,
    experience_years,
    qualification,
    department_id,
    salary,
    is_active,
    created_at,
    updated_at
  ) VALUES (
    school_id,
    teacher_data->>'employee_id',
    teacher_data->>'first_name',
    teacher_data->>'last_name',
    teacher_data->>'email',
    teacher_data->>'phone',
    CASE 
      WHEN teacher_data->>'hire_date' IS NOT NULL 
      THEN (teacher_data->>'hire_date')::date 
      ELSE CURRENT_DATE 
    END,
    COALESCE((teacher_data->>'experience_years')::integer, 0),
    teacher_data->>'qualification',
    CASE 
      WHEN teacher_data->>'department_id' IS NOT NULL AND teacher_data->>'department_id' != 'null'
      THEN (teacher_data->>'department_id')::uuid 
      ELSE NULL 
    END,
    CASE 
      WHEN teacher_data->>'salary' IS NOT NULL 
      THEN (teacher_data->>'salary')::decimal 
      ELSE NULL 
    END,
    true,
    NOW(),
    NOW()
  ) RETURNING id INTO new_teacher_id;
  
  RETURN new_teacher_id;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION register_student(jsonb, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION register_teacher(jsonb, uuid) TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION register_student(jsonb, uuid) IS 'Registers a new student bypassing RLS policies for self-registration';
COMMENT ON FUNCTION register_teacher(jsonb, uuid) IS 'Registers a new teacher bypassing RLS policies for self-registration';
