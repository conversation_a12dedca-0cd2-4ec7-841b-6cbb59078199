-- COMPLETE SETUP FOR SCHOOL2 PLATFORM
-- Run this entire script in Supabase SQL Editor

-- Step 1: Disable RLS for development (IMPORTANT!)
ALTER TABLE schools DISABLE ROW LEVEL SECURITY;
ALTER TABLE classes DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE subjects DISABLE ROW LEVEL SECURITY;
ALTER TABLE examinations DISABLE ROW LEVEL SECURITY;
ALTER TABLE results DISABLE ROW LEVEL SECURITY;
ALTER TABLE teachers DISABLE ROW LEVEL SECURITY;
ALTER TABLE timetable DISABLE ROW LEVEL SECURITY;
ALTER TABLE assignments DISABLE ROW LEVEL SECURITY;
ALTER TABLE attendance DISABLE ROW LEVEL SECURITY;
ALTER TABLE communications DISABLE ROW LEVEL SECURITY;
ALTER TABLE departments DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE parents DISABLE ROW LEVEL SECURITY;
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Step 2: Get school2 ID (should already exist)
SELECT id, name, slug FROM schools WHERE slug = 'school2';

-- Step 3: Create basic classes for school2 (replace with actual school ID)
-- First, get the school ID from step 2, then replace 'REPLACE_WITH_SCHOOL_ID' below

DO $$
DECLARE
    school_uuid UUID;
BEGIN
    -- Get school2 ID
    SELECT id INTO school_uuid FROM schools WHERE slug = 'school2';
    
    IF school_uuid IS NOT NULL THEN
        -- Insert classes
        INSERT INTO classes (
            school_id,
            name,
            grade_level,
            section,
            academic_year,
            max_students,
            is_active
        ) VALUES 
            (school_uuid, 'Form One', '1', 'A', '2024-25', 40, true),
            (school_uuid, 'Form One', '1', 'B', '2024-25', 35, true),
            (school_uuid, 'Form Two', '2', 'A', '2024-25', 38, true),
            (school_uuid, 'Form Two', '2', 'B', '2024-25', 42, true),
            (school_uuid, 'Form Three', '3', 'A', '2024-25', 30, true),
            (school_uuid, 'Form Four', '4', 'A', '2024-25', 25, true)
        ON CONFLICT DO NOTHING;
        
        -- Insert subjects
        INSERT INTO subjects (
            school_id,
            name,
            code,
            description,
            is_active
        ) VALUES 
            (school_uuid, 'Mathematics', 'MATH', 'Core mathematics curriculum', true),
            (school_uuid, 'Physics', 'PHYS', 'Physical sciences and laboratory work', true),
            (school_uuid, 'Chemistry', 'CHEM', 'Chemical sciences and practical work', true),
            (school_uuid, 'Biology', 'BIO', 'Life sciences and biological studies', true),
            (school_uuid, 'English', 'ENG', 'English language and literature', true),
            (school_uuid, 'History', 'HIST', 'World and local history studies', true)
        ON CONFLICT DO NOTHING;
        
        -- Insert sample students
        INSERT INTO students (
            school_id,
            student_id,
            first_name,
            last_name,
            parent_email,
            phone,
            address,
            date_of_birth,
            gender,
            class_id,
            admission_date,
            is_active
        ) VALUES 
            (school_uuid, 'STU001', 'John', 'Smith', '<EMAIL>', '+1234567890', '123 Main St', '2008-05-15', 'Male', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form One' AND section = 'A' LIMIT 1), '2023-09-01', true),
            (school_uuid, 'STU002', 'Emily', 'Johnson', '<EMAIL>', '+1234567891', '456 Oak Ave', '2008-08-22', 'Female', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form One' AND section = 'A' LIMIT 1), '2023-09-01', true),
            (school_uuid, 'STU003', 'Michael', 'Chen', '<EMAIL>', '+1234567892', '789 Pine Rd', '2007-12-10', 'Male', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form One' AND section = 'B' LIMIT 1), '2023-09-01', true),
            (school_uuid, 'STU004', 'Sarah', 'Williams', '<EMAIL>', '+1234567893', '321 Elm St', '2007-03-18', 'Female', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form One' AND section = 'B' LIMIT 1), '2023-09-01', true),
            (school_uuid, 'STU005', 'David', 'Brown', '<EMAIL>', '+1234567894', '654 Maple Dr', '2006-11-25', 'Male', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form Two' AND section = 'A' LIMIT 1), '2023-09-01', true),
            (school_uuid, 'STU006', 'Lisa', 'Davis', '<EMAIL>', '+1234567895', '987 Cedar Ln', '2006-07-08', 'Female', 
             (SELECT id FROM classes WHERE school_id = school_uuid AND name = 'Form Two' AND section = 'B' LIMIT 1), '2023-09-01', true)
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'School2 setup completed successfully!';
    ELSE
        RAISE NOTICE 'School2 not found! Please create it first.';
    END IF;
END $$;

-- Step 4: Verify setup
SELECT 
    s.name as school_name,
    s.slug,
    s.email,
    (SELECT COUNT(*) FROM classes WHERE school_id = s.id) as total_classes,
    (SELECT COUNT(*) FROM subjects WHERE school_id = s.id) as total_subjects,
    (SELECT COUNT(*) FROM students WHERE school_id = s.id) as total_students
FROM schools s 
WHERE s.slug = 'school2';

-- Step 5: Show class-student distribution
SELECT 
    c.name || ' ' || c.section as class_name,
    COUNT(s.id) as student_count
FROM classes c
LEFT JOIN students s ON c.id = s.class_id
WHERE c.school_id = (SELECT id FROM schools WHERE slug = 'school2')
GROUP BY c.id, c.name, c.section
ORDER BY c.grade_level, c.section;
