-- Complete Setup Script for School2 in Supabase
-- Run this in the Supabase SQL Editor

-- 1. First, let's check if school2 already exists
SELECT id, name, slug, email FROM schools WHERE slug = 'school2';

-- 2. If school2 doesn't exist, create it
INSERT INTO schools (
  name,
  slug,
  address,
  email,
  phone,
  website,
  established_year,
  principal_name,
  is_active
) VALUES (
  'Demo School 2',
  'school2',
  '123 Education Street, Learning City, LC 12345',
  '<EMAIL>',
  '******-0123',
  'https://school2.edu',
  2020,
  'Dr<PERSON> <PERSON>',
  true
) ON CONFLICT (slug) DO NOTHING;

-- 3. Get the school ID for school2 (run this to get the ID)
SELECT id FROM schools WHERE slug = 'school2';

-- 4. IMPORTANT: Replace 'YOUR_SCHOOL_ID_HERE' with the actual UUID from step 3
-- Then uncomment and run the following sections:

/*
-- 5. Create basic classes for school2
INSERT INTO classes (
  school_id,
  name,
  grade_level,
  section,
  academic_year,
  max_students,
  is_active
) VALUES
  ('YOUR_SCHOOL_ID_HERE', 'Form One', '1', 'A', '2024-25', 40, true),
  ('YOUR_SCHOOL_ID_HERE', 'Form One', '1', 'B', '2024-25', 35, true),
  ('YOUR_SCHOOL_ID_HERE', 'Form Two', '2', 'A', '2024-25', 38, true),
  ('YOUR_SCHOOL_ID_HERE', 'Form Two', '2', 'B', '2024-25', 42, true),
  ('YOUR_SCHOOL_ID_HERE', 'Form Three', '3', 'A', '2024-25', 30, true),
  ('YOUR_SCHOOL_ID_HERE', 'Form Four', '4', 'A', '2024-25', 25, true);

-- 6. Create basic subjects for school2
INSERT INTO subjects (
  school_id,
  name,
  code,
  description,
  is_active
) VALUES
  ('YOUR_SCHOOL_ID_HERE', 'Mathematics', 'MATH', 'Core mathematics curriculum', true),
  ('YOUR_SCHOOL_ID_HERE', 'Physics', 'PHYS', 'Physical sciences and laboratory work', true),
  ('YOUR_SCHOOL_ID_HERE', 'Chemistry', 'CHEM', 'Chemical sciences and practical work', true),
  ('YOUR_SCHOOL_ID_HERE', 'Biology', 'BIO', 'Life sciences and biological studies', true),
  ('YOUR_SCHOOL_ID_HERE', 'English', 'ENG', 'English language and literature', true),
  ('YOUR_SCHOOL_ID_HERE', 'History', 'HIST', 'World and local history studies', true);

-- 7. Create some sample students for testing
INSERT INTO students (
  school_id,
  student_id,
  first_name,
  last_name,
  parent_email,
  phone,
  address,
  date_of_birth,
  gender,
  class_id,
  admission_date,
  is_active
) VALUES
  ('YOUR_SCHOOL_ID_HERE', 'STU001', 'John', 'Smith', '<EMAIL>', '+1234567890', '123 Main St', '2008-05-15', 'Male', (SELECT id FROM classes WHERE school_id = 'YOUR_SCHOOL_ID_HERE' AND name = 'Form One' AND section = 'A'), '2023-09-01', true),
  ('YOUR_SCHOOL_ID_HERE', 'STU002', 'Emily', 'Johnson', '<EMAIL>', '+1234567891', '456 Oak Ave', '2008-08-22', 'Female', (SELECT id FROM classes WHERE school_id = 'YOUR_SCHOOL_ID_HERE' AND name = 'Form One' AND section = 'A'), '2023-09-01', true),
  ('YOUR_SCHOOL_ID_HERE', 'STU003', 'Michael', 'Chen', '<EMAIL>', '+1234567892', '789 Pine Rd', '2007-12-10', 'Male', (SELECT id FROM classes WHERE school_id = 'YOUR_SCHOOL_ID_HERE' AND name = 'Form One' AND section = 'B'), '2023-09-01', true);
*/

-- 8. TEMPORARY: Disable RLS for development (ONLY FOR TESTING!)
-- WARNING: This removes all security - use only in development
-- Uncomment these lines if you're having RLS issues:

/*
ALTER TABLE schools DISABLE ROW LEVEL SECURITY;
ALTER TABLE classes DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE subjects DISABLE ROW LEVEL SECURITY;
ALTER TABLE examinations DISABLE ROW LEVEL SECURITY;
ALTER TABLE results DISABLE ROW LEVEL SECURITY;
ALTER TABLE teachers DISABLE ROW LEVEL SECURITY;
ALTER TABLE timetable DISABLE ROW LEVEL SECURITY;
*/

-- 9. Verify everything was created successfully
SELECT
  s.name as school_name,
  s.slug,
  s.email,
  (SELECT COUNT(*) FROM classes WHERE school_id = s.id) as total_classes,
  (SELECT COUNT(*) FROM subjects WHERE school_id = s.id) as total_subjects,
  (SELECT COUNT(*) FROM students WHERE school_id = s.id) as total_students
FROM schools s
WHERE s.slug = 'school2';
