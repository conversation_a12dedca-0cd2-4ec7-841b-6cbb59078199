<!DOCTYPE html>
<html>
<head>
    <title>Test Supabase Auth</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Supabase Authentication</h1>
    <button onclick="testAuth()">Test Login</button>
    <div id="result"></div>

    <script>
        const supabaseUrl = 'https://rkqndobgwwkzlleidbrp.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJrcW5kb2Jnd3dremxsZWlkYnJwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1OTQ1NDksImV4cCI6MjA2NzE3MDU0OX0.OgH2Gw5NOjQCnkfgpf66w9LK5rKzZGKN2F2sL4OoGYU';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        async function testAuth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing Supabase auth...');
                
                // Test 1: Get session
                const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
                console.log('Session test:', { sessionData, sessionError });
                
                // Test 2: Sign in
                const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: '151515'
                });
                console.log('Sign in test:', { signInData, signInError });
                
                if (signInError) {
                    resultDiv.innerHTML = `Sign in error: ${signInError.message}`;
                    return;
                }
                
                // Test 3: Get profile
                const { data: profileData, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', signInData.user.id)
                    .single();
                console.log('Profile test:', { profileData, profileError });
                
                if (profileError) {
                    resultDiv.innerHTML = `Profile error: ${profileError.message}`;
                    return;
                }
                
                resultDiv.innerHTML = `Success! User: ${profileData.first_name} ${profileData.last_name}`;
                
            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
