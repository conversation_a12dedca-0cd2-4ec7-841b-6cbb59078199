import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useParams } from "react-router-dom";
import { AuthProvider } from "@/contexts/NewAuthContext";

import { NewProtectedRoute } from "@/components/NewProtectedRoute";
import { NewLayout } from "@/components/NewLayout";
import { LandingPage } from "@/pages/LandingPage";
import { FreshLogin } from "@/pages/FreshLogin";
import { SimpleDashboard } from "@/pages/SimpleDashboard";

// Import all existing pages
import { Students } from "@/pages/Students";
import { Teachers } from "@/pages/Teachers";
import { Classes } from "@/pages/Classes";
import { Subjects } from "@/pages/Subjects";
import { Departments } from "@/pages/Departments";
import { Attendance } from "@/pages/Attendance";
import { Timetable } from "@/pages/Timetable";
import { Assignments } from "@/pages/Assignments";
import { Examinations } from "@/pages/Examinations";
import { Grades } from "@/pages/Grades";
import { Results } from "@/pages/Results";
import { Fees } from "@/pages/Fees";
import { Communications } from "@/pages/Communications";
import { Reports } from "@/pages/Reports";
import { Notifications } from "@/pages/Notifications";
import { Settings } from "@/pages/Settings";
import { UserManagement } from "@/pages/UserManagement";
import { Parents } from "@/pages/Parents";

// Import student and teacher specific pages
import { StudentTimetable } from "@/pages/student/StudentTimetable";
import { StudentAssignments } from "@/pages/student/StudentAssignments";
import { StudentExaminations } from "@/pages/student/StudentExaminations";
import { StudentResults } from "@/pages/student/StudentResults";
import { TeacherTimetable } from "@/pages/teacher/TeacherTimetable";

const queryClient = new QueryClient();

// Component to handle school root redirect
const SchoolRedirect = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  return <Navigate to={`/${schoolSlug}/dashboard`} replace />;
};

function FreshApp() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <Routes>
              {/* Landing page */}
              <Route path="/" element={<LandingPage />} />
              
              {/* School-specific routes */}
              <Route path="/:schoolSlug" element={<SchoolRedirect />} />
              <Route path="/:schoolSlug/login" element={<FreshLogin />} />
              
              {/* Protected routes with layout */}
              <Route path="/:schoolSlug/dashboard" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <SimpleDashboard />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/students" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Students />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/teachers" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <Teachers />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/parents" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <Parents />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/classes" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Classes />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/subjects" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Subjects />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/departments" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <Departments />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/attendance" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Attendance />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/timetable" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Timetable />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/student-timetable" element={
                <NewProtectedRoute requiredRole="student">
                  <NewLayout>
                    <StudentTimetable />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/teacher-timetable" element={
                <NewProtectedRoute requiredRole="teacher">
                  <NewLayout>
                    <TeacherTimetable />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/assignments" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Assignments />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/student-assignments" element={
                <NewProtectedRoute requiredRole="student">
                  <NewLayout>
                    <StudentAssignments />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/examinations" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Examinations />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/student-examinations" element={
                <NewProtectedRoute requiredRole="student">
                  <NewLayout>
                    <StudentExaminations />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/grades" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Grades />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/results" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Results />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/student-results" element={
                <NewProtectedRoute requiredRole="student">
                  <NewLayout>
                    <StudentResults />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/fees" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Fees />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/communications" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Communications />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/reports" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <Reports />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/notifications" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <Notifications />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/settings" element={
                <NewProtectedRoute>
                  <NewLayout>
                    <Settings />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              <Route path="/:schoolSlug/user-management" element={
                <NewProtectedRoute requiredRole="admin">
                  <NewLayout>
                    <UserManagement />
                  </NewLayout>
                </NewProtectedRoute>
              } />
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default FreshApp;
