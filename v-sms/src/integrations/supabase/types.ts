export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      assignment_submissions: {
        Row: {
          id: string
          school_id: string
          assignment_id: string
          student_id: string
          submission_text: string | null
          attachments: <PERSON><PERSON>
          submitted_at: string
          marks_obtained: number | null
          feedback: string | null
          graded_by: string | null
          graded_at: string | null
          is_late: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          assignment_id: string
          student_id: string
          submission_text?: string | null
          attachments?: Json
          submitted_at?: string
          marks_obtained?: number | null
          feedback?: string | null
          graded_by?: string | null
          graded_at?: string | null
          is_late?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          assignment_id?: string
          student_id?: string
          submission_text?: string | null
          attachments?: J<PERSON>
          submitted_at?: string
          marks_obtained?: number | null
          feedback?: string | null
          graded_by?: string | null
          graded_at?: string | null
          is_late?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      assignments: {
        Row: {
          id: string
          school_id: string
          class_id: string
          subject_id: string
          teacher_id: string
          title: string
          description: string | null
          due_date: string
          max_marks: number
          status: Database["public"]["Enums"]["assignment_status"]
          attachments: Json
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          class_id: string
          subject_id: string
          teacher_id: string
          title: string
          description?: string | null
          due_date: string
          max_marks?: number
          status?: Database["public"]["Enums"]["assignment_status"]
          attachments?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          class_id?: string
          subject_id?: string
          teacher_id?: string
          title?: string
          description?: string | null
          due_date?: string
          max_marks?: number
          status?: Database["public"]["Enums"]["assignment_status"]
          attachments?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      attendance: {
        Row: {
          id: string
          school_id: string
          student_id: string
          class_id: string
          date: string
          status: Database["public"]["Enums"]["attendance_status"]
          remarks: string | null
          marked_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          student_id: string
          class_id: string
          date: string
          status: Database["public"]["Enums"]["attendance_status"]
          remarks?: string | null
          marked_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          student_id?: string
          class_id?: string
          date?: string
          status?: Database["public"]["Enums"]["attendance_status"]
          remarks?: string | null
          marked_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      classes: {
        Row: {
          id: string
          school_id: string
          name: string
          grade_level: string
          section: string | null
          class_teacher_id: string | null
          academic_year: string
          max_students: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          name: string
          grade_level: string
          section?: string | null
          class_teacher_id?: string | null
          academic_year: string
          max_students?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          name?: string
          grade_level?: string
          section?: string | null
          class_teacher_id?: string | null
          academic_year?: string
          max_students?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      communications: {
        Row: {
          id: string
          school_id: string
          sender_id: string
          recipient_id: string | null
          recipient_class_id: string | null
          subject: string
          message: string
          message_type: string
          attachments: Json
          is_read: boolean
          is_important: boolean
          parent_message_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          sender_id: string
          recipient_id?: string | null
          recipient_class_id?: string | null
          subject: string
          message: string
          message_type?: string
          attachments?: Json
          is_read?: boolean
          is_important?: boolean
          parent_message_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          sender_id?: string
          recipient_id?: string | null
          recipient_class_id?: string | null
          subject?: string
          message?: string
          message_type?: string
          attachments?: Json
          is_read?: boolean
          is_important?: boolean
          parent_message_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      departments: {
        Row: {
          id: string
          school_id: string
          name: string
          description: string | null
          head_teacher_id: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          name: string
          description?: string | null
          head_teacher_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          name?: string
          description?: string | null
          head_teacher_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      events: {
        Row: {
          id: string
          school_id: string
          title: string
          description: string | null
          event_date: string
          start_time: string | null
          end_time: string | null
          location: string | null
          event_type: string | null
          target_classes: string[]
          is_public: boolean
          created_by: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          title: string
          description?: string | null
          event_date: string
          start_time?: string | null
          end_time?: string | null
          location?: string | null
          event_type?: string | null
          target_classes?: string[]
          is_public?: boolean
          created_by?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          title?: string
          description?: string | null
          event_date?: string
          start_time?: string | null
          end_time?: string | null
          location?: string | null
          event_type?: string | null
          target_classes?: string[]
          is_public?: boolean
          created_by?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      exam_subjects: {
        Row: {
          id: string
          school_id: string
          examination_id: string
          subject_id: string
          class_id: string
          exam_date: string
          start_time: string
          end_time: string
          max_marks: number
          room_number: string | null
          invigilator_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          school_id: string
          examination_id: string
          subject_id: string
          class_id: string
          exam_date: string
          start_time: string
          end_time: string
          max_marks?: number
          room_number?: string | null
          invigilator_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          examination_id?: string
          subject_id?: string
          class_id?: string
          exam_date?: string
          start_time?: string
          end_time?: string
          max_marks?: number
          room_number?: string | null
          invigilator_id?: string | null
          created_at?: string
        }
      }
      examinations: {
        Row: {
          id: string
          school_id: string
          name: string
          description: string | null
          exam_type: string
          academic_year: string
          start_date: string
          end_date: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          name: string
          description?: string | null
          exam_type: string
          academic_year: string
          start_date: string
          end_date: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          name?: string
          description?: string | null
          exam_type?: string
          academic_year?: string
          start_date?: string
          end_date?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      fee_payments: {
        Row: {
          id: string
          school_id: string
          student_id: string
          fee_structure_id: string
          amount_paid: number
          payment_date: string
          payment_method: string | null
          transaction_id: string | null
          status: Database["public"]["Enums"]["fee_status"]
          remarks: string | null
          received_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          student_id: string
          fee_structure_id: string
          amount_paid: number
          payment_date: string
          payment_method?: string | null
          transaction_id?: string | null
          status?: Database["public"]["Enums"]["fee_status"]
          remarks?: string | null
          received_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          student_id?: string
          fee_structure_id?: string
          amount_paid?: number
          payment_date?: string
          payment_method?: string | null
          transaction_id?: string | null
          status?: Database["public"]["Enums"]["fee_status"]
          remarks?: string | null
          received_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      fee_structures: {
        Row: {
          id: string
          school_id: string
          class_id: string
          fee_type: string
          amount: number
          academic_year: string
          due_date: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          class_id: string
          fee_type: string
          amount: number
          academic_year: string
          due_date?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          class_id?: string
          fee_type?: string
          amount?: number
          academic_year?: string
          due_date?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          school_id: string
          title: string
          message: string
          notification_type: Database["public"]["Enums"]["notification_type"]
          target_role: Database["public"]["Enums"]["user_role"] | null
          target_user_id: string | null
          target_class_id: string | null
          is_read: boolean
          priority: Database["public"]["Enums"]["notification_priority"]
          expires_at: string | null
          created_by: string | null
          created_at: string
        }
        Insert: {
          id?: string
          school_id: string
          title: string
          message: string
          notification_type?: Database["public"]["Enums"]["notification_type"]
          target_role?: Database["public"]["Enums"]["user_role"] | null
          target_user_id?: string | null
          target_class_id?: string | null
          is_read?: boolean
          priority?: Database["public"]["Enums"]["notification_priority"]
          expires_at?: string | null
          created_by?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          title?: string
          message?: string
          notification_type?: Database["public"]["Enums"]["notification_type"]
          target_role?: Database["public"]["Enums"]["user_role"] | null
          target_user_id?: string | null
          target_class_id?: string | null
          is_read?: boolean
          priority?: Database["public"]["Enums"]["notification_priority"]
          expires_at?: string | null
          created_by?: string | null
          created_at?: string
        }
      }
      parents: {
        Row: {
          id: string
          school_id: string
          profile_id: string | null
          occupation: string | null
          workplace: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          profile_id?: string | null
          occupation?: string | null
          workplace?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          profile_id?: string | null
          occupation?: string | null
          workplace?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          school_id: string
          role: Database["public"]["Enums"]["user_role"]
          first_name: string | null
          last_name: string | null
          email: string | null
          phone: string | null
          address: string | null
          date_of_birth: string | null
          gender: string | null
          avatar_url: string | null
          is_active: boolean
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          school_id: string
          role?: Database["public"]["Enums"]["user_role"]
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          phone?: string | null
          address?: string | null
          date_of_birth?: string | null
          gender?: string | null
          avatar_url?: string | null
          is_active?: boolean
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          role?: Database["public"]["Enums"]["user_role"]
          first_name?: string | null
          last_name?: string | null
          email?: string | null
          phone?: string | null
          address?: string | null
          date_of_birth?: string | null
          gender?: string | null
          avatar_url?: string | null
          is_active?: boolean
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
      }
      results: {
        Row: {
          id: string
          school_id: string
          student_id: string
          exam_subject_id: string
          marks_obtained: number
          grade: string | null
          remarks: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          student_id: string
          exam_subject_id: string
          marks_obtained: number
          grade?: string | null
          remarks?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          student_id?: string
          exam_subject_id?: string
          marks_obtained?: number
          grade?: string | null
          remarks?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      schools: {
        Row: {
          id: string
          name: string
          slug: string
          domain: string | null
          logo: string | null
          address: string
          phone: string | null
          email: string
          website: string | null
          established_year: number | null
          principal_name: string | null
          total_students: number
          total_teachers: number
          settings: Json
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          domain?: string | null
          logo?: string | null
          address: string
          phone?: string | null
          email: string
          website?: string | null
          established_year?: number | null
          principal_name?: string | null
          total_students?: number
          total_teachers?: number
          settings?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          domain?: string | null
          logo?: string | null
          address?: string
          phone?: string | null
          email?: string
          website?: string | null
          established_year?: number | null
          principal_name?: string | null
          total_students?: number
          total_teachers?: number
          settings?: Json
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      students: {
        Row: {
          id: string
          school_id: string
          profile_id: string | null
          student_id: string
          class_id: string | null
          admission_date: string
          roll_number: string | null
          parent_contact: string | null
          emergency_contact: string | null
          medical_info: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          profile_id?: string | null
          student_id: string
          class_id?: string | null
          admission_date: string
          roll_number?: string | null
          parent_contact?: string | null
          emergency_contact?: string | null
          medical_info?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          profile_id?: string | null
          student_id?: string
          class_id?: string | null
          admission_date?: string
          roll_number?: string | null
          parent_contact?: string | null
          emergency_contact?: string | null
          medical_info?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      student_parents: {
        Row: {
          id: string
          school_id: string
          student_id: string
          parent_id: string
          relationship: string
          is_primary: boolean
          created_at: string
        }
        Insert: {
          id?: string
          school_id: string
          student_id: string
          parent_id: string
          relationship: string
          is_primary?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          student_id?: string
          parent_id?: string
          relationship?: string
          is_primary?: boolean
          created_at?: string
        }
      }
      subjects: {
        Row: {
          id: string
          school_id: string
          department_id: string | null
          name: string
          code: string | null
          description: string | null
          credits: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          department_id?: string | null
          name: string
          code?: string | null
          description?: string | null
          credits?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          department_id?: string | null
          name?: string
          code?: string | null
          description?: string | null
          credits?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      teachers: {
        Row: {
          id: string
          school_id: string
          profile_id: string | null
          employee_id: string
          department_id: string | null
          hire_date: string
          qualification: string | null
          experience_years: number
          salary: number | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          profile_id?: string | null
          employee_id: string
          department_id?: string | null
          hire_date: string
          qualification?: string | null
          experience_years?: number
          salary?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          profile_id?: string | null
          employee_id?: string
          department_id?: string | null
          hire_date?: string
          qualification?: string | null
          experience_years?: number
          salary?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      timetable: {
        Row: {
          id: string
          school_id: string
          class_id: string
          subject_id: string
          teacher_id: string
          day_of_week: number
          start_time: string
          end_time: string
          room_number: string | null
          academic_year: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          school_id: string
          class_id: string
          subject_id: string
          teacher_id: string
          day_of_week: number
          start_time: string
          end_time: string
          room_number?: string | null
          academic_year: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          school_id?: string
          class_id?: string
          subject_id?: string
          teacher_id?: string
          day_of_week?: number
          start_time?: string
          end_time?: string
          room_number?: string | null
          academic_year?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_user_school_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
    }
    Enums: {
      assignment_status: "draft" | "published" | "closed"
      attendance_status: "present" | "absent" | "late" | "excused"
      fee_status: "pending" | "partial" | "paid" | "overdue"
      notification_priority: "low" | "normal" | "high" | "urgent"
      notification_type: "general" | "academic" | "fee" | "attendance" | "exam" | "event"
      user_role: "super_admin" | "admin" | "teacher" | "student" | "parent"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
