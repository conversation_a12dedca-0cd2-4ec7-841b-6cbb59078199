import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { GraduationCap, Menu, X, Search, LogIn, Building2 } from 'lucide-react';

export const PublicHeader: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);
  const [schoolSlug, setSchoolSlug] = React.useState('');
  const navigate = useNavigate();

  const handleSchoolAccess = (e: React.FormEvent) => {
    e.preventDefault();
    if (schoolSlug.trim()) {
      navigate(`/${schoolSlug.trim()}/login`);
    }
  };

  return (
    <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">SchoolMS</span>
          </Link>

          <div className="hidden md:flex items-center gap-6">
            <Link to="/features" className="text-gray-600 hover:text-green-600 transition-colors">Features</Link>
            <Link to="/about" className="text-gray-600 hover:text-green-600 transition-colors">About</Link>
            <Link to="/contact" className="text-gray-600 hover:text-green-600 transition-colors">Contact</Link>

            {/* School Access Form */}
            <form onSubmit={handleSchoolAccess} className="flex items-center gap-2">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Enter school name"
                  value={schoolSlug}
                  onChange={(e) => setSchoolSlug(e.target.value)}
                  className="w-40 h-9 text-sm pr-8"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                  sms.app/
                </div>
              </div>
              <Button type="submit" size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                <LogIn className="h-4 w-4" />
              </Button>
            </form>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white"
                onClick={() => navigate('/register-school')}
              >
                <Building2 className="mr-2 h-4 w-4" />
                Register School
              </Button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-gray-600"
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-4 py-6 space-y-4">
              <Link
                to="/features"
                className="block text-gray-600 hover:text-green-600 transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Features
              </Link>
              <Link
                to="/about"
                className="block text-gray-600 hover:text-green-600 transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                About
              </Link>
              <Link
                to="/contact"
                className="block text-gray-600 hover:text-green-600 transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Contact
              </Link>

              {/* Mobile School Access */}
              <div className="pt-4 space-y-3">
                <div className="text-sm font-medium text-gray-700 mb-2">Access Your School</div>
                <form onSubmit={handleSchoolAccess} className="space-y-3">
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder="Enter school name"
                      value={schoolSlug}
                      onChange={(e) => setSchoolSlug(e.target.value)}
                      className="w-full pr-16"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                      sms.app/
                    </div>
                  </div>
                  <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-white">
                    <LogIn className="mr-2 h-4 w-4" />
                    Access School Portal
                  </Button>
                </form>

                <Button
                  variant="outline"
                  className="w-full border-green-600 text-green-600 hover:bg-green-600 hover:text-white"
                  onClick={() => {
                    navigate('/register-school');
                    setMobileMenuOpen(false);
                  }}
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  Register New School
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
