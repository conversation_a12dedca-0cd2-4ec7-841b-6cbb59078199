import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { User, Mail, Phone, MapPin, Calendar, MoreVertical, Edit, Trash2, Eye } from 'lucide-react';

interface StudentCardProps {
  student: {
    id: string;
    studentId: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    class: string;
    section: string;
    rollNumber: string;
    gender: string;
    dateOfBirth: string;
    address: string;
    admissionDate: string;
    emergencyContact: string;
    parentContact: string;
    medicalInfo: string;
    bloodGroup: string;
    isActive: boolean;
    hasCredentials: boolean;
    loginEmail: string;
    loginPassword: string;
  };
  onEdit?: (student: any) => void;
  onDelete?: (student: any) => void;
  onView?: (student: any) => void;

}

export const StudentCard: React.FC<StudentCardProps> = ({
  student,
  onEdit,
  onDelete,
  onView
}) => {
  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-lg text-gray-900">
                {student.firstName} {student.lastName}
              </h3>
              <p className="text-sm text-gray-600">Student ID: {student.studentId}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(student)}>
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(student)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </DropdownMenuItem>
              )}

              {onDelete && (
                <DropdownMenuItem onClick={() => onDelete(student)} className="text-red-600">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Class:</span>
            <span className="text-sm font-medium">{student.class}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Roll Number:</span>
            <span className="text-sm font-medium">{student.rollNumber}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Gender:</span>
            <span className="text-sm font-medium">{student.gender}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Date of Birth:</span>
            <span className="text-sm font-medium">
              {student.dateOfBirth ? new Date(student.dateOfBirth).toLocaleDateString() : 'Not provided'}
            </span>
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex items-start space-x-2">
            <Mail className="w-4 h-4 text-gray-400 mt-0.5" />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-600">Parent's Email:</p>
              <p className="text-sm font-medium truncate">{student.email}</p>
            </div>
          </div>
          <div className="flex items-start space-x-2">
            <Phone className="w-4 h-4 text-gray-400 mt-0.5" />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-600">Contact:</p>
              <p className="text-sm font-medium">{student.phone}</p>
            </div>
          </div>
          {student.address && (
            <div className="flex items-start space-x-2">
              <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-600">Address:</p>
                <p className="text-sm font-medium">{student.address}</p>
              </div>
            </div>
          )}
        </div>

        {/* Login Credentials Section */}
        <div className="bg-blue-50 rounded-lg p-3 mb-4 border border-blue-200">
          <h4 className="text-sm font-medium text-blue-800 mb-2">Dashboard Access</h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-xs text-gray-600">Email:</span>
              <span className="text-xs font-mono">{student.loginEmail}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-xs text-gray-600">Password:</span>
              <span className="text-xs font-mono">{student.loginPassword}</span>
            </div>
            <div className="text-xs text-blue-600 font-medium mt-2 text-center">
              Auto-generated credentials
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <Badge
              variant="secondary"
              className={`text-xs ${student.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
            >
              {student.isActive ? "Active" : "Inactive"}
            </Badge>
            {student.hasCredentials && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs">
                Has Login
              </Badge>
            )}
          </div>
          <div className="text-xs text-gray-500">
            Admitted: {student.admissionDate ? new Date(student.admissionDate).toLocaleDateString() : 'N/A'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
