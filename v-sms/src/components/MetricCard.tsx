
import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: "increase" | "decrease" | "neutral";
  icon: ReactNode;
  iconBg: string;
}

export const MetricCard = ({
  title,
  value,
  change,
  changeType = "neutral",
  icon,
  iconBg,
}: MetricCardProps) => {
  const changeColor = {
    increase: "text-green-600",
    decrease: "text-red-600",
    neutral: "text-gray-600",
  }[changeType];

  return (
    <Card className="stat-card">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-3xl font-bold mt-2">{value}</p>
            {change && (
              <p className={`text-sm mt-1 ${changeColor}`}>
                {change}
              </p>
            )}
          </div>
          <div className={`metric-icon ${iconBg}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
