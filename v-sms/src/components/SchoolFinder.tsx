import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  LogIn, 
  Building2, 
  Users, 
  GraduationCap,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface School {
  id: string;
  name: string;
  slug: string;
  address?: string;
  total_students?: number;
  total_teachers?: number;
  is_active: boolean;
}

interface SchoolFinderProps {
  variant?: 'compact' | 'full';
  showSuggestions?: boolean;
  placeholder?: string;
  className?: string;
}

export const SchoolFinder: React.FC<SchoolFinderProps> = ({
  variant = 'compact',
  showSuggestions = false,
  placeholder = 'Enter your school name',
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const navigate = useNavigate();

  // Search for schools
  const searchSchools = async (term: string) => {
    if (term.length < 2) {
      setSchools([]);
      setShowResults(false);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('schools')
        .select('id, name, slug, address, total_students, total_teachers, is_active')
        .eq('is_active', true)
        .or(`name.ilike.%${term}%,slug.ilike.%${term}%`)
        .limit(5);

      if (error) throw error;
      
      setSchools(data || []);
      setShowResults(true);
    } catch (error) {
      console.error('Error searching schools:', error);
      setSchools([]);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (showSuggestions) {
        searchSchools(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, showSuggestions]);

  const handleDirectAccess = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      const slug = searchTerm.trim().toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
      navigate(`/${slug}/login`);
    }
  };

  const handleSchoolSelect = (school: School) => {
    navigate(`/${school.slug}/login`);
    setShowResults(false);
    setSearchTerm('');
  };

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`}>
        <form onSubmit={handleDirectAccess} className="flex items-center gap-2">
          <div className="relative">
            <Input
              type="text"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-20"
              onFocus={() => showSuggestions && setShowResults(true)}
              onBlur={() => setTimeout(() => setShowResults(false), 200)}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
              sms.app/
            </div>
          </div>
          <Button type="submit" size="sm" className="bg-green-600 hover:bg-green-700">
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <LogIn className="h-4 w-4" />}
          </Button>
        </form>

        {/* Search Results Dropdown */}
        {showSuggestions && showResults && schools.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            {schools.map((school) => (
              <button
                key={school.id}
                onClick={() => handleSchoolSelect(school)}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">{school.name}</div>
                    <div className="text-sm text-gray-500">sms.app/{school.slug}</div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Find Your School
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleDirectAccess} className="space-y-4">
          <div className="relative">
            <Input
              type="text"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-20"
              onFocus={() => setShowResults(true)}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
              sms.app/
            </div>
          </div>
          
          <Button type="submit" className="w-full bg-green-600 hover:bg-green-700">
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <LogIn className="mr-2 h-4 w-4" />
                Access School Portal
              </>
            )}
          </Button>
        </form>

        {/* Search Results */}
        {showResults && schools.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-700">Suggested Schools:</div>
            {schools.map((school) => (
              <button
                key={school.id}
                onClick={() => handleSchoolSelect(school)}
                className="w-full p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{school.name}</div>
                    <div className="text-sm text-gray-500">sms.app/{school.slug}</div>
                    {school.address && (
                      <div className="text-xs text-gray-400 mt-1">{school.address}</div>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    {school.total_students && (
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {school.total_students}
                      </div>
                    )}
                    <ArrowRight className="h-4 w-4" />
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}

        {showResults && searchTerm.length >= 2 && schools.length === 0 && !loading && (
          <div className="text-center py-4 text-gray-500">
            <Building2 className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p>No schools found matching "{searchTerm}"</p>
            <p className="text-sm mt-1">Try a different search term or contact your school administrator</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
