import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/NewAuthContext';
import { UserRole } from '@/types';
import { Shield, GraduationCap, User, Heart } from 'lucide-react';

const demoUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin' as UserRole,
    description: 'Full access to all features'
  },
  {
    id: '2',
    email: '<EMAIL>',
    username: 'teacher1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'teacher' as UserRole,
    description: 'Access to assigned classes, students, grades, assignments'
  },
  {
    id: '3',
    email: '<EMAIL>',
    username: 'parent1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'parent' as User<PERSON><PERSON>,
    description: 'Access to children\'s results, examinations, fees'
  }
];

const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return <Shield className="h-5 w-5 text-red-500" />;
    case 'teacher':
      return <GraduationCap className="h-5 w-5 text-blue-500" />;
    case 'parent':
      return <Heart className="h-5 w-5 text-purple-500" />;
    default:
      return <User className="h-5 w-5 text-gray-500" />;
  }
};

const getRoleColor = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'teacher':
      return 'bg-blue-100 text-blue-800';
    case 'parent':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const UserSwitcher: React.FC = () => {
  const { user, login } = useAuth();

  const handleUserSwitch = async (email: string) => {
    // Get the password for the user
    const userPassword = email.includes('admin') ? 'admin123' :
                        email.includes('teacher') ? 'teacher123' : 'parent123';

    await login(email, userPassword);
    // No need to reload - React state will update automatically
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-6 w-6" />
          User Role Switcher
        </CardTitle>
        <p className="text-sm text-gray-600">
          Switch between different user roles to test role-based access controls
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {demoUsers.map((demoUser) => (
            <div
              key={demoUser.id}
              className={`p-4 border rounded-lg transition-all ${
                user?.email === demoUser.email
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-3 mb-3">
                {getRoleIcon(demoUser.role)}
                <div>
                  <h3 className="font-medium text-sm">
                    {demoUser.firstName} {demoUser.lastName}
                  </h3>
                  <Badge variant="secondary" className={`text-xs ${getRoleColor(demoUser.role)}`}>
                    {demoUser.role.charAt(0).toUpperCase() + demoUser.role.slice(1)}
                  </Badge>
                </div>
              </div>
              
              <p className="text-xs text-gray-600 mb-3">
                {demoUser.description}
              </p>

              <div className="space-y-1 text-xs text-gray-500 mb-3">
                <div>Email: {demoUser.email}</div>
                <div>Username: {demoUser.username}</div>
              </div>

              {user?.email === demoUser.email ? (
                <Badge variant="default" className="w-full justify-center">
                  Current User
                </Badge>
              ) : (
                <Button
                  onClick={() => handleUserSwitch(demoUser.email)}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  Switch to {demoUser.role}
                </Button>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-medium text-yellow-800 mb-2">Role-Based Access Summary:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong className="text-red-700">Admin:</strong>
              <ul className="list-disc list-inside text-gray-700 ml-2">
                <li>Full access to all features</li>
                <li>Can create, edit, delete all data</li>
                <li>Access to all students, teachers, classes</li>
              </ul>
            </div>
            <div>
              <strong className="text-blue-700">Teacher:</strong>
              <ul className="list-disc list-inside text-gray-700 ml-2">
                <li>Access to assigned classes only</li>
                <li>Can manage students in their classes</li>
                <li>Can create/edit assignments, grades, attendance</li>
              </ul>
            </div>

            <div>
              <strong className="text-purple-700">Parent:</strong>
              <ul className="list-disc list-inside text-gray-700 ml-2">
                <li>Access to children's academic records</li>
                <li>View results, examinations, and fees</li>
                <li>Manage children's fee payments</li>
                <li>Cannot access other students' data</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
