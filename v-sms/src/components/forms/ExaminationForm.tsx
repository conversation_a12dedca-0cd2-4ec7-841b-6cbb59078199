import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

const examinationSchema = z.object({
  name: z.string().min(3, 'Examination name must be at least 3 characters'),
  description: z.string().optional(),
  subjectId: z.string().min(1, 'Subject is required'),
  classId: z.string().min(1, 'Class is required'),
  examType: z.string().min(1, 'Exam type is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  startTime: z.string().min(1, 'Start time is required'),
  endTime: z.string().min(1, 'End time is required'),
  totalMarks: z.number().min(1, 'Total marks must be at least 1'),
  passingMarks: z.number().min(1, 'Passing marks must be at least 1'),
  room: z.string().optional(),
  instructions: z.string().optional(),
});

type ExaminationFormData = z.infer<typeof examinationSchema>;

interface ExaminationFormProps {
  examination?: any;
  subjects?: any[];
  classes?: any[];
  onSubmit: (data: ExaminationFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const examTypes = [
  { value: 'midterm', label: 'Midterm Exam' },
  { value: 'final', label: 'Final Exam' },
  { value: 'quiz', label: 'Quiz' },
  { value: 'test', label: 'Class Test' },
  { value: 'practical', label: 'Practical Exam' },
  { value: 'oral', label: 'Oral Exam' }
];

const rooms = ['Room 201', 'Room 202', 'Lab 3', 'Chemistry Lab', 'Physics Lab', 'Room 105'];

export const ExaminationForm: React.FC<ExaminationFormProps> = ({
  examination,
  subjects = [],
  classes = [],
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [startDate, setStartDate] = useState<Date | undefined>(
    examination?.start_date ? new Date(examination.start_date) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    examination?.end_date ? new Date(examination.end_date) : undefined
  );
  const [selectedClass, setSelectedClass] = useState(examination?.class_id || '');
  const [selectedSubject, setSelectedSubject] = useState(examination?.subject_id || '');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ExaminationFormData>({
    resolver: zodResolver(examinationSchema),
    defaultValues: {
      name: examination?.name || '',
      description: examination?.description || '',
      subjectId: examination?.subject_id || '',
      classId: examination?.class_id || '',
      examType: examination?.type || '',
      startDate: examination?.start_date || '',
      endDate: examination?.end_date || '',
      startTime: examination?.start_time || '',
      endTime: examination?.end_time || '',
      totalMarks: examination?.total_marks || 100,
      passingMarks: examination?.passing_marks || 40,
      room: examination?.room || '',
      instructions: examination?.instructions || '',
    }
  });

  const handleStartDateSelect = (date: Date | undefined) => {
    setStartDate(date);
    if (date) {
      setValue('startDate', date.toISOString().split('T')[0]);
    }
  };

  const handleEndDateSelect = (date: Date | undefined) => {
    setEndDate(date);
    if (date) {
      setValue('endDate', date.toISOString().split('T')[0]);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium mb-4">Examination Details</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Examination Name *</Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="e.g., Mathematics Midterm Exam"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Brief description of the examination..."
              rows={3}
            />
          </div>

          {/* Class Selection - Primary Field */}
          <div className="space-y-2 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Label htmlFor="classId" className="text-blue-800 font-medium">Target Class *</Label>
            <Select onValueChange={(value) => {
              setValue('classId', value);
              setSelectedClass(value);
            }} value={watch('classId') || selectedClass}>
              <SelectTrigger className="border-blue-300">
                <SelectValue placeholder="Select class - All students in this class will take this exam" />
              </SelectTrigger>
              <SelectContent>
                {classes.map(cls => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name} {cls.section}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.classId && (
              <p className="text-sm text-red-600">{errors.classId.message}</p>
            )}
            {selectedClass && (
              <p className="text-sm text-blue-600 mt-2">
                📝 This examination will be scheduled for all students in the selected class
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subjectId">Subject *</Label>
              <Select onValueChange={(value) => {
                setValue('subjectId', value);
                setSelectedSubject(value);
              }} value={watch('subjectId') || selectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name} ({subject.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.subjectId && (
                <p className="text-sm text-red-600">{errors.subjectId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="examType">Exam Type *</Label>
              <Select onValueChange={(value) => setValue('examType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select exam type" />
                </SelectTrigger>
                <SelectContent>
                  {examTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.examType && (
                <p className="text-sm text-red-600">{errors.examType.message}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Schedule */}
      <div>
        <h3 className="text-lg font-medium mb-4">Schedule</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Select start date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={handleStartDateSelect}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.startDate && (
                <p className="text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>End Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Select end date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={handleEndDateSelect}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.endDate && (
                <p className="text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">Start Time *</Label>
              <Input
                id="startTime"
                type="time"
                {...register('startTime')}
              />
              {errors.startTime && (
                <p className="text-sm text-red-600">{errors.startTime.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">End Time *</Label>
              <Input
                id="endTime"
                type="time"
                {...register('endTime')}
              />
              {errors.endTime && (
                <p className="text-sm text-red-600">{errors.endTime.message}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Marks & Settings */}
      <div>
        <h3 className="text-lg font-medium mb-4">Marks & Settings</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                type="number"
                {...register('totalMarks', { valueAsNumber: true })}
                placeholder="100"
              />
              {errors.totalMarks && (
                <p className="text-sm text-red-600">{errors.totalMarks.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="passingMarks">Passing Marks *</Label>
              <Input
                id="passingMarks"
                type="number"
                {...register('passingMarks', { valueAsNumber: true })}
                placeholder="40"
              />
              {errors.passingMarks && (
                <p className="text-sm text-red-600">{errors.passingMarks.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="room">Room/Venue</Label>
              <Select onValueChange={(value) => setValue('room', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select room" />
                </SelectTrigger>
                <SelectContent>
                  {rooms.map(room => (
                    <SelectItem key={room} value={room}>{room}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="instructions">Instructions</Label>
            <Textarea
              id="instructions"
              {...register('instructions')}
              placeholder="Special instructions for students..."
              rows={3}
            />
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <span className="animate-spin mr-2">⏳</span>
              {examination ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            examination ? 'Update Examination' : 'Create Examination'
          )}
        </Button>
      </div>
    </form>
  );
};
