import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Textarea } from '@/components/ui/textarea';
import { Class } from '@/types';

const classSchema = z.object({
  name: z.string().min(3, 'Class name must be at least 3 characters'),
  grade: z.string().min(1, 'Grade/Form is required').max(20, 'Grade/Form must be less than 20 characters'),
  description: z.string().optional(),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  academicYear: z.string().min(1, 'Academic year is required'),
});

type ClassFormData = z.infer<typeof classSchema>;

interface ClassFormProps {
  class?: Class;
  onSubmit: (data: ClassFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}



export const ClassForm: React.FC<ClassFormProps> = ({
  class: classData,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ClassFormData>({
    resolver: zodResolver(classSchema),
    defaultValues: classData ? {
      name: classData.name,
      grade: classData.grade,
      description: classData.description || '',
      capacity: classData.capacity,
      academicYear: classData.academicYear,
    } : {
      name: '',
      grade: '',
      description: '',
      capacity: 30,
      academicYear: '2024-2025',
    }
  });

  const selectedGrade = watch('grade');

  // Auto-generate class name when grade changes
  React.useEffect(() => {
    if (selectedGrade) {
      // Clean up the grade input and use it as the class name
      const gradeText = selectedGrade.trim();
      setValue('name', gradeText);
    }
  }, [selectedGrade, setValue]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-base sm:text-lg font-medium mb-4">Class Information</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="grade">Grade/Form *</Label>
              <Input
                id="grade"
                {...register('grade')}
                placeholder="e.g., Grade 1, Form 1, S1, Year 7, etc."
              />
              <p className="text-xs text-gray-500">
                Enter grade for primary (Grade 1-7) or form for secondary (Form 1-6, S1-S6, Year 7-13)
              </p>
              {errors.grade && (
                <p className="text-sm text-red-600">{errors.grade.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Class Name *</Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="e.g., Grade 10 Section A"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Brief description of the class..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="capacity">Capacity *</Label>
            <Input
              id="capacity"
              type="number"
              {...register('capacity', { valueAsNumber: true })}
              placeholder="30"
              className="max-w-xs"
            />
            {errors.capacity && (
              <p className="text-sm text-red-600">{errors.capacity.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Class Teacher assignment will be added later when teacher relationships are set up */}

            <div className="space-y-2">
              <Label htmlFor="academicYear">Academic Year *</Label>
              <Input
                id="academicYear"
                {...register('academicYear')}
                placeholder="2024-2025"
              />
              {errors.academicYear && (
                <p className="text-sm text-red-600">{errors.academicYear.message}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Subjects and Schedule will be managed on separate pages */}

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Button type="submit" disabled={isLoading} className="flex-1 h-11">
          {isLoading ? 'Saving...' : classData ? 'Update Class' : 'Create Class'}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1 h-11">
          Cancel
        </Button>
      </div>
    </form>
  );
};
