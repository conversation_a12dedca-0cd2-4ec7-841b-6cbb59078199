
import { Bell, Search, User, LogOut, <PERSON>ting<PERSON>, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/contexts/NewAuthContext";
import { MobileNavMenu } from "./MobileNavMenu";

export const Header = () => {
  const { user, signOut } = useAuth();

  const getInitials = (email: string) => {
    return email ? email.charAt(0).toUpperCase() : 'U';
  };

  const getDisplayName = (email: string) => {
    return email ? email.split('@')[0] : 'User';
  };

  const getRoleColor = () => {
    return 'bg-red-500'; // Default to admin color
  };

  return (
    <header className="gradient-header h-14 sm:h-16 px-3 sm:px-4 md:px-6 flex items-center justify-between text-white sticky top-0 z-50">
      <div className="flex items-center">
        {/* Mobile Navigation Menu - Visible only on mobile */}
        <MobileNavMenu />
      </div>

      <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
        <Button variant="ghost" size="icon" className="text-white hover:bg-white/10 hidden sm:flex h-8 w-8 sm:h-10 sm:w-10">
          <Search className="h-4 w-4 sm:h-5 sm:w-5" />
        </Button>
        <Button variant="ghost" size="icon" className="text-white hover:bg-white/10 relative h-8 w-8 sm:h-10 sm:w-10">
          <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
          <span className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center text-[9px] sm:text-[10px] md:text-xs">
            3
          </span>
        </Button>

        {user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="text-white hover:bg-white/10 flex items-center gap-1 sm:gap-2 px-1 sm:px-2 md:px-3 h-8 sm:h-10">
                <Avatar className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8">
                  <AvatarImage src="" />
                  <AvatarFallback className={`${getRoleColor()} text-white text-xs sm:text-sm`}>
                    {getInitials(user?.email || '')}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left hidden lg:block">
                  <p className="text-sm font-medium truncate max-w-24">{getDisplayName(user?.email || '')}</p>
                  <p className="text-xs text-white/80 capitalize">admin</p>
                </div>
                <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div>
                  <p className="font-medium">{getDisplayName(user?.email || '')}</p>
                  <p className="text-sm text-gray-500">{user?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={signOut} className="text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};
