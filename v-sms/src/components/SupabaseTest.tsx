import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const SupabaseTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setTestResult('Testing connection...');
    
    try {
      // Test 1: Basic connection
      const { data: connectionTest, error: connectionError } = await supabase
        .from('users')
        .select('count', { count: 'exact' });
      
      if (connectionError) {
        setTestResult(`Connection Error: ${connectionError.message}`);
        return;
      }
      
      setTestResult(`✅ Connection successful! Found ${connectionTest?.length || 0} users`);
      
      // Test 2: Try to get all users
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('*');
      
      if (usersError) {
        setTestResult(prev => prev + `\n❌ Users query error: ${usersError.message}`);
        return;
      }
      
      setTestResult(prev => prev + `\n✅ Users query successful! Found ${users?.length || 0} users`);
      
      // Test 3: Try authentication
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        setTestResult(prev => prev + `\n❌ Auth error: ${authError.message}`);
      } else {
        setTestResult(prev => prev + `\n✅ Auth check successful! Session: ${authData.session ? 'Active' : 'None'}`);
      }
      
    } catch (error) {
      setTestResult(`❌ Unexpected error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setTestResult('Testing login...');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin123'
      });

      if (error) {
        setTestResult(`❌ Login error: ${error.message}`);
        return;
      }

      setTestResult(`✅ Login successful! User: ${data.user?.email}`);

      // Now try to get the user profile
      if (data.user) {
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('auth_id', data.user.id)
          .single();

        if (profileError) {
          setTestResult(prev => prev + `\n❌ Profile error: ${profileError.message}`);

          // Try fallback by email
          const { data: emailProfile, error: emailError } = await supabase
            .from('users')
            .select('*')
            .eq('email', data.user.email)
            .single();

          if (emailError) {
            setTestResult(prev => prev + `\n❌ Email fallback error: ${emailError.message}`);
          } else {
            setTestResult(prev => prev + `\n✅ Profile loaded by email: ${emailProfile.first_name} ${emailProfile.last_name} (${emailProfile.role})`);
          }
        } else {
          setTestResult(prev => prev + `\n✅ Profile loaded: ${profile.first_name} ${profile.last_name} (${profile.role})`);
        }

        // Test logout
        const { error: logoutError } = await supabase.auth.signOut();
        if (logoutError) {
          setTestResult(prev => prev + `\n❌ Logout error: ${logoutError.message}`);
        } else {
          setTestResult(prev => prev + `\n✅ Logout successful`);
        }
      }

    } catch (error) {
      setTestResult(`❌ Unexpected error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={testConnection} disabled={loading}>
            Test Connection
          </Button>
          <Button onClick={testLogin} disabled={loading}>
            Test Login
          </Button>
        </div>
        
        {testResult && (
          <div className="bg-gray-100 p-4 rounded-lg">
            <pre className="whitespace-pre-wrap text-sm">{testResult}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
