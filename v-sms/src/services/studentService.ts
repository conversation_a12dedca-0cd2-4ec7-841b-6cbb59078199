import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

type Student = Database['public']['Tables']['students']['Row'];
type StudentInsert = Database['public']['Tables']['students']['Insert'];
type StudentUpdate = Database['public']['Tables']['students']['Update'];

export const studentService = {
  // Get all students (admin only)
  async getAll(): Promise<Student[]> {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        classes:class_id (
          id,
          name,
          grade_level,
          section
        )
      `)
      .eq('is_active', true)
      .order('first_name');

    if (error) throw error;
    return data || [];
  },

  // Get students by class ID
  async getByClassId(classId: string): Promise<Student[]> {
    const { data, error } = await supabase
      .from('students')
      .select('*')
      .eq('class_id', classId)
      .eq('is_active', true)
      .order('first_name');

    if (error) throw error;
    return data || [];
  },

  // Get student by ID
  async getById(id: string): Promise<Student | null> {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        classes:class_id (
          id,
          name,
          grade_level,
          section
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new student
  async create(student: StudentInsert): Promise<Student> {
    const { data, error } = await supabase
      .from('students')
      .insert(student)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update student
  async update(id: string, updates: StudentUpdate): Promise<Student> {
    const { data, error } = await supabase
      .from('students')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete student (soft delete)
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('students')
      .update({ is_active: false })
      .eq('id', id);

    if (error) throw error;
  },

  // Get students for a teacher (based on assigned classes)
  async getByTeacherId(teacherId: string): Promise<Student[]> {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        classes:class_id!inner (
          id,
          name,
          grade_level,
          section,
          teacher_classes!inner (
            teacher_id
          )
        )
      `)
      .eq('classes.teacher_classes.teacher_id', teacherId)
      .eq('is_active', true)
      .order('first_name');

    if (error) throw error;
    return data || [];
  },

  // Get children for a parent
  async getByParentId(parentId: string): Promise<Student[]> {
    const { data, error } = await supabase
      .from('students')
      .select(`
        *,
        classes:class_id (
          id,
          name,
          grade_level,
          section
        ),
        parent_students!inner (
          parent_id
        )
      `)
      .eq('parent_students.parent_id', parentId)
      .eq('is_active', true)
      .order('first_name');

    if (error) throw error;
    return data || [];
  }
};
