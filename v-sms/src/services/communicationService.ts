import { supabase } from '@/integrations/supabase/client';
import { MultiTenantService } from './multiTenantService';

// Communication Service
export class CommunicationService extends MultiTenantService {
  // Create new communication
  static async createCommunication(commData: {
    title: string;
    message: string;
    type: 'announcement' | 'notice' | 'circular' | 'event';
    target_audience: 'all' | 'teachers' | 'parents' | 'students' | 'specific_class';
    target_class_id?: string;
    attachment_url?: string;
    is_urgent?: boolean;
    is_published?: boolean;
  }) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { data, error } = await supabaseAdmin
        .from('communications')
        .insert({
          ...commData,
          school_id: schoolId,
          sender_id: userId,
          published_at: commData.is_published ? new Date().toISOString() : null,
        })
        .select()
        .single();

      if (error) throw error;

      // Create notifications for target audience
      if (commData.is_published) {
        await this.createNotificationsForCommunication(data.id, commData.target_audience, commData.target_class_id);
      }

      return data;
    });
  }

  // Get all communications
  static async getAllCommunications() {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabase
        .from('communications')
        .select(`
          *,
          sender:users!sender_id(first_name, last_name, role),
          target_class:classes(name)
        `)
        .eq('school_id', schoolId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    });
  }

  // Get communications for current user
  static async getUserCommunications() {
    return this.withSchoolContext(async (schoolId, userId, role) => {
      let query = supabase
        .from('communications')
        .select(`
          *,
          sender:users!sender_id(first_name, last_name, role),
          target_class:classes(name)
        `)
        .eq('school_id', schoolId)
        .eq('is_published', true);

      // Filter based on user role
      if (role !== 'admin') {
        query = query.or(`target_audience.eq.all,target_audience.eq.${role}`);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    });
  }

  // Publish communication
  static async publishCommunication(commId: string) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabaseAdmin
        .from('communications')
        .update({ 
          is_published: true,
          published_at: new Date().toISOString()
        })
        .eq('id', commId)
        .eq('school_id', schoolId)
        .select()
        .single();

      if (error) throw error;

      // Create notifications
      await this.createNotificationsForCommunication(
        data.id, 
        data.target_audience, 
        data.target_class_id
      );

      return data;
    });
  }

  // Create notifications for communication
  private static async createNotificationsForCommunication(
    commId: string, 
    targetAudience: string, 
    targetClassId?: string
  ) {
    return this.withSchoolContext(async (schoolId) => {
      let userQuery = supabase
        .from('users')
        .select('id')
        .eq('school_id', schoolId)
        .eq('is_active', true);

      // Filter users based on target audience
      if (targetAudience !== 'all') {
        if (targetAudience === 'specific_class' && targetClassId) {
          // Get parents of students in specific class
          const { data: students } = await supabase
            .from('students')
            .select('id')
            .eq('class_id', targetClassId);

          if (students && students.length > 0) {
            const { data: parentStudents } = await supabase
              .from('parent_students')
              .select('parent_id')
              .in('student_id', students.map(s => s.id));

            if (parentStudents && parentStudents.length > 0) {
              userQuery = userQuery.in('id', parentStudents.map(ps => ps.parent_id));
            }
          }
        } else {
          userQuery = userQuery.eq('role', targetAudience);
        }
      }

      const { data: users, error: usersError } = await userQuery;
      if (usersError || !users) return;

      const notifications = users.map(user => ({
        school_id: schoolId,
        user_id: user.id,
        title: 'New Communication',
        message: 'You have a new communication to read',
        type: 'communication',
        reference_id: commId,
        reference_type: 'communication'
      }));

      const { error: notifError } = await supabaseAdmin
        .from('notifications')
        .insert(notifications);

      if (notifError) console.error('Failed to create notifications:', notifError);
    });
  }
}

// Notification Service
export class NotificationService extends MultiTenantService {
  // Get user notifications
  static async getUserNotifications(limit = 50) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('school_id', schoolId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    });
  }

  // Mark notification as read
  static async markAsRead(notificationId: string) {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { data, error } = await supabaseAdmin
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('school_id', schoolId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }

  // Mark all notifications as read
  static async markAllAsRead() {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { data, error } = await supabaseAdmin
        .from('notifications')
        .update({ is_read: true })
        .eq('school_id', schoolId)
        .eq('user_id', userId)
        .eq('is_read', false)
        .select();

      if (error) throw error;
      return data;
    });
  }

  // Get unread notification count
  static async getUnreadCount() {
    return this.withSchoolContext(async (schoolId, userId) => {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('school_id', schoolId)
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) throw error;
      return count || 0;
    });
  }

  // Create custom notification
  static async createNotification(notifData: {
    user_id: string;
    title: string;
    message: string;
    type: string;
    reference_id?: string;
    reference_type?: string;
  }) {
    return this.withSchoolContext(async (schoolId) => {
      const { data, error } = await supabaseAdmin
        .from('notifications')
        .insert({
          ...notifData,
          school_id: schoolId,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    });
  }
}
