// Single User School Management Services
// All services use local data storage

// Core Services
export { 
  authService, 
  userService, 
  studentService, 
  teacherService, 
  assignmentService, 
  classService, 
  resultService, 
  periodService 
} from './dataService';

// Multi-tenant services
export { MultiTenantDataService } from './multiTenantDataService';
export { MultiTenantService } from './multiTenantService';

// Export services with capitalized names and expected method names for compatibility
import { studentService, teacherService, classService, assignmentService } from './dataService';

export const StudentService = {
  ...studentService,
  async getAllStudents() {
    const response = await studentService.getAll();
    return response.success ? response.data : [];
  },
  async createStudent(data: any) {
    const response = await studentService.create(data);
    return response.success ? response.data : null;
  }
};

export const TeacherService = {
  ...teacherService,
  async getAllTeachers() {
    const response = await teacherService.getAll();
    return response.success ? response.data : [];
  }
};

export const ClassService = {
  ...classService,
  async getAllClasses() {
    const response = await classService.getAllClasses();
    return response.success ? response.data : [];
  }
};

export const AssignmentService = {
  ...assignmentService,
  async getAllAssignments() {
    const response = await assignmentService.getAll();
    return response.success ? response.data : [];
  }
};

export const TimetableService = {
  async getAllTimetable() {
    return [];
  },
  async createTimetableEntry(data: any) {
    return { id: '1', ...data };
  }
};

// Mock services that don't exist yet but are imported by pages
export const AttendanceService = {
  async getAllAttendance() {
    return [];
  },
  async getAttendanceByDate(date: string) {
    return [];
  },
  async createAttendance(data: any) {
    return { id: '1', ...data };
  },
  async updateAttendance(id: string, data: any) {
    return { id, ...data };
  },
  async deleteAttendance(id: string) {
    return true;
  }
};

export const CommunicationService = {
  async getAllCommunications() {
    return [];
  },
  async createCommunication(data: any) {
    return { id: '1', ...data };
  }
};

export const DepartmentService = {
  async getAllDepartments() {
    return [];
  },
  async createDepartment(data: any) {
    return { id: '1', ...data };
  }
};

export const ExaminationService = {
  async getAllExaminations() {
    return [];
  },
  async createExamination(data: any) {
    return { id: '1', ...data };
  }
};

export const FeeService = {
  async getAllFees() {
    return [];
  },
  async createFee(data: any) {
    return { id: '1', ...data };
  }
};

export const ParentService = {
  async getAllParents() {
    // Return mock parent data
    return [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '************',
        address: '123 Main St, City',
        children: ['Student 1', 'Student 2'],
        occupation: 'Engineer',
        emergencyContact: '************',
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '************',
        address: '456 Oak Ave, Town',
        children: ['Student 3'],
        occupation: 'Doctor',
        emergencyContact: '************',
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    ];
  },
  async createParent(data: any) {
    return { id: '1', ...data };
  }
};

export const SubjectService = {
  async getAllSubjects() {
    return [];
  },
  async createSubject(data: any) {
    return { id: '1', ...data };
  }
};

// Service Types
export interface SchoolContext {
  userId: string;
  role: 'admin' | 'teacher' | 'parent';
}

export interface Student {
  id: string;
  student_id: string;
  first_name: string;
  last_name: string;
  roll_number?: string;
  date_of_birth?: string;
  gender?: string;
  address?: string;
  phone?: string;
  email?: string;
  admission_date?: string;
  admission_number?: string;
  emergency_contact?: string;
  blood_group?: string;
  medical_conditions?: string;
  photo_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Teacher {
  id: string;
  employee_id: string;
  qualification?: string;
  experience_years: number;
  joining_date?: string;
  salary?: number;
  emergency_contact?: string;
  blood_group?: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
}

export interface Class {
  id: string;
  name: string;
  grade_level?: number;
  section?: string;
  class_teacher_id?: string;
  max_students: number;
  room_number?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: string;
  name: string;
  code?: string;
  description?: string;
  grade_levels?: number[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AttendanceRecord {
  id: string;
  student_id: string;
  class_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  remarks?: string;
  marked_by: string;
  marked_at: string;
  created_at: string;
}

export interface Examination {
  id: string;
  name: string;
  type: string;
  start_date: string;
  end_date: string;
  total_marks: number;
  passing_marks: number;
  description?: string;
  is_published: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Grade {
  id: string;
  examination_id: string;
  student_id: string;
  subject_id: string;
  marks_obtained: number;
  total_marks: number;
  grade?: string;
  remarks?: string;
  teacher_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Communication {
  id: string;
  sender_id: string;
  title: string;
  message: string;
  type: 'announcement' | 'notice' | 'circular' | 'event';
  target_audience: 'all' | 'teachers' | 'parents' | 'students' | 'specific_class';
  target_class_id?: string;
  attachment_url?: string;
  is_urgent: boolean;
  is_published: boolean;
  published_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Fee {
  id: string;
  class_id?: string;
  student_id?: string;
  fee_type: string;
  amount: number;
  due_date: string;
  description?: string;
  is_recurring: boolean;
  recurrence_period?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface FeePayment {
  id: string;
  fee_id: string;
  student_id: string;
  amount_paid: number;
  payment_date: string;
  payment_method?: string;
  transaction_id?: string;
  receipt_number?: string;
  remarks?: string;
  collected_by: string;
  created_at: string;
}

export interface TimetableEntry {
  id: string;
  class_id: string;
  subject_id: string;
  teacher_id: string;
  day_of_week: number;
  period_number: number;
  start_time: string;
  end_time: string;
  room_number?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: string;
  reference_id?: string;
  reference_type?: string;
  is_read: boolean;
  created_at: string;
}

// Utility functions for role-based access (simplified for single user)
export const hasPermission = (userRole: string, requiredRole: string[]): boolean => {
  return true; // Single user has all permissions
};

export const canAccessStudent = (userRole: string, userId: string, studentData: any): boolean => {
  return true; // Single user can access all students
};

// Dashboard data aggregation (using local data)
export const getDashboardData = async (role: string) => {
  try {
    // Return mock data for dashboard
    return {
      success: true,
      data: [
        [], // students
        [], // teachers  
        [], // classes
        { totalOutstanding: 0, overdueAmount: 0 }, // fees
        [], // notifications
        []  // communications
      ]
    };
  } catch (error) {
    return {
      success: false,
      error: error as Error
    };
  }
};
