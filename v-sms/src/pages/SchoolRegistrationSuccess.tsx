import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  Copy, 
  ExternalLink, 
  Eye, 
  EyeOff, 
  School,
  Users,
  LogIn,
  ArrowRight,
  Shield,
  User,
  GraduationCap,
  Heart
} from 'lucide-react';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';
import { useToast } from '@/hooks/use-toast';

interface SchoolRegistrationSuccessProps {}

export const SchoolRegistrationSuccess: React.FC<SchoolRegistrationSuccessProps> = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const [copied, setCopied] = useState<string | null>(null);

  const { school, adminCredentials, schoolPortalLink } = location.state || {};

  useEffect(() => {
    if (!school || !adminCredentials) {
      navigate('/');
    }
  }, [school, adminCredentials, navigate]);

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(type);
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard`,
      });
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const demoUsers = [
    {
      role: 'Admin',
      email: adminCredentials?.email,
      password: adminCredentials?.password,
      icon: Shield,
      color: 'bg-red-100 text-red-800',
      description: 'Full access to all school management features'
    },
    {
      role: 'Teacher',
      email: `teacher@${school?.slug}.demo`,
      password: 'demo123',
      icon: User,
      color: 'bg-blue-100 text-blue-800',
      description: 'Manage classes, assignments, and student grades'
    },
    {
      role: 'Student',
      email: `student@${school?.slug}.demo`,
      password: 'demo123',
      icon: GraduationCap,
      color: 'bg-green-100 text-green-800',
      description: 'View assignments, grades, and school announcements'
    },
    {
      role: 'Parent',
      email: `parent@${school?.slug}.demo`,
      password: 'demo123',
      icon: Heart,
      color: 'bg-purple-100 text-purple-800',
      description: 'Monitor child\'s progress and communicate with teachers'
    }
  ];

  if (!school || !adminCredentials) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <PublicHeader />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Success Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🎉 School Registration Successful!
            </h1>
            <p className="text-xl text-gray-600 mb-6">
              <strong>{school.name}</strong> has been successfully registered and is ready to use.
            </p>
            <Badge className="bg-green-100 text-green-800 text-lg px-4 py-2">
              <School className="mr-2 h-5 w-5" />
              Portal URL: sms.app/{school.slug}
            </Badge>
          </div>

          {/* Admin Credentials */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-6 w-6 text-red-600" />
                Your Admin Credentials
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Important:</strong> Save these credentials securely. You'll need them to access your school portal.
                </AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 p-3 bg-gray-50 rounded-lg font-mono text-sm">
                      {adminCredentials.email}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(adminCredentials.email, 'Email')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Password</label>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 p-3 bg-gray-50 rounded-lg font-mono text-sm">
                      {showPassword ? adminCredentials.password : '••••••••••••'}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(adminCredentials.password, 'Password')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demo Users */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-6 w-6 text-blue-600" />
                Demo User Accounts
              </CardTitle>
              <p className="text-gray-600">
                We've created demo accounts for each user type to help you explore the system.
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {demoUsers.map((user, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={`p-2 rounded-lg ${user.color}`}>
                        <user.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{user.role}</h3>
                        <p className="text-sm text-gray-600">{user.description}</p>
                      </div>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium">Email:</span> {user.email}
                      </div>
                      <div>
                        <span className="font-medium">Password:</span> {user.password}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white"
              onClick={() => window.open(schoolPortalLink, '_blank')}
            >
              <LogIn className="mr-2 h-5 w-5" />
              Access Your School Portal
              <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
            
            <Button
              size="lg"
              variant="outline"
              onClick={() => navigate('/')}
            >
              Return to Homepage
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          {/* Next Steps */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-semibold text-blue-600">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Login with your admin credentials</h4>
                    <p className="text-gray-600">Use the credentials above to access your school portal</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-semibold text-blue-600">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Customize your school settings</h4>
                    <p className="text-gray-600">Update school information, themes, and preferences</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm font-semibold text-blue-600">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Add teachers, students, and parents</h4>
                    <p className="text-gray-600">Start building your school community</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <PublicFooter />
    </div>
  );
};
