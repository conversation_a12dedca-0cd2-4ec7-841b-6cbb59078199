
import React from "react";
import { <PERSON> } from "react-router-dom";
import { GraduationCap } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export const Privacy: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex flex-col">
    {/* Header */}
    <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">VertiQ</span>
          </div>
          <div className="hidden md:flex items-center gap-8">
            <Link to="/features" className="text-gray-600 hover:text-green-600 transition-colors">Features</Link>
            <Link to="/pricing" className="text-gray-600 hover:text-green-600 transition-colors">Pricing</Link>
            <Link to="/about" className="text-gray-600 hover:text-green-600 transition-colors">About</Link>
            <Link to="/contact" className="text-gray-600 hover:text-green-600 transition-colors">Contact</Link>
            <Link to="/login">
              <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>

    {/* Main Content */}
    <main className="flex-1 flex flex-col items-center justify-center py-24 px-4">
      <Badge className="mb-6 bg-green-100 text-green-800 border-green-200">
        Privacy Policy
      </Badge>
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
      <p className="text-lg text-gray-600 max-w-2xl mb-6 text-center">
        We respect your privacy. Read how VertiQ school management system collects, uses, and protects your information.
      </p>
      <div className="bg-white rounded-xl shadow-md p-6 max-w-2xl text-gray-700">
        <h2 className="font-bold mb-2">Your Data</h2>
        <p className="mb-4">
          We only collect data needed to provide you with the best school management experience via VertiQ school management system.
        </p>
        <h2 className="font-bold mb-2">How We Use It</h2>
        <p className="mb-4">
          Your data is never sold or shared with third parties. We use it to improve our platform and services.
        </p>
        <h2 className="font-bold mb-2">Contact</h2>
        <p>
          Questions? <a href="mailto:<EMAIL>" className="text-green-600 underline"><EMAIL></a>
        </p>
      </div>
    </main>

    {/* Footer */}
    <footer className="bg-gray-900 text-white py-12 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold">VertiQ</span>
            </div>
            <p className="text-gray-400">
              School management made seamless with VertiQ school management system.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Product</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/features" className="hover:text-white transition-colors">Features</Link></li>
              <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link to="/documentation" className="hover:text-white transition-colors">Documentation</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
              <li><Link to="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-gray-400">
              <li><Link to="/help-center" className="hover:text-white transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 VertiQ. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
);

export default Privacy;
