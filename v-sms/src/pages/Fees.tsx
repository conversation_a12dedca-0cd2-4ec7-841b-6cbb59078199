import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import {
  Search,
  Plus,
  CreditCard,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Receipt,
  Send,
  Eye,
  Building,
  Globe,
  MoreVertical,
  User,
  Calendar
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { MetricCard } from "@/components/MetricCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { FeeService } from "@/services";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// Mock fee data
const mockFeeData = [
  {
    id: '1',
    studentId: 'S001',
    studentName: 'Alice Johnson',
    class: '10-A',
    type: 'tuition',
    amount: 1500,
    dueDate: '2024-12-31',
    status: 'pending',
    paidAmount: 0,
    description: 'Term 2 Tuition Fee',
    createdAt: '2024-11-01'
  },
  {
    id: '2',
    studentId: 'S001',
    studentName: 'Alice Johnson',
    class: '10-A',
    type: 'transport',
    amount: 300,
    dueDate: '2024-12-15',
    status: 'paid',
    paidAmount: 300,
    paidDate: '2024-12-01',
    paymentMethod: 'Credit Card',
    transactionId: 'TXN123456',
    description: 'Monthly Transport Fee',
    createdAt: '2024-11-01'
  },
  {
    id: '3',
    studentId: 'S002',
    studentName: 'Bob Smith',
    class: '11-B',
    type: 'tuition',
    amount: 1500,
    dueDate: '2024-11-30',
    status: 'overdue',
    paidAmount: 0,
    description: 'Term 2 Tuition Fee',
    createdAt: '2024-10-01'
  },
  {
    id: '4',
    studentId: 'S003',
    studentName: 'Charlie Brown',
    class: '12-A',
    type: 'lab',
    amount: 200,
    dueDate: '2024-12-20',
    status: 'partial',
    paidAmount: 100,
    paidDate: '2024-12-05',
    paymentMethod: 'Bank Transfer',
    description: 'Laboratory Fee',
    createdAt: '2024-11-15'
  }
];

const feeTypes = ['tuition', 'transport', 'library', 'lab', 'sports', 'other'];
const paymentMethods = ['Cash', 'Credit Card', 'Debit Card', 'Bank Transfer', 'Online Payment'];

// Mock student data
const mockStudents = [
  { id: 'S001', name: 'Alice Johnson', class: '10-A', rollNumber: '001' },
  { id: 'S002', name: 'Bob Smith', class: '11-B', rollNumber: '002' },
  { id: 'S003', name: 'Charlie Brown', class: '12-A', rollNumber: '003' },
  { id: 'S004', name: 'Diana Prince', class: '10-A', rollNumber: '004' },
  { id: 'S005', name: 'Edward Norton', class: '11-B', rollNumber: '005' },
  { id: 'S006', name: 'Fiona Green', class: '12-A', rollNumber: '006' },
  { id: 'S007', name: 'George Wilson', class: '10-B', rollNumber: '007' },
  { id: 'S008', name: 'Helen Davis', class: '11-A', rollNumber: '008' },
  { id: 'S009', name: 'Ivan Rodriguez', class: '12-B', rollNumber: '009' },
  { id: 'S010', name: 'Julia Martinez', class: '10-A', rollNumber: '010' },
];

const classes = ['10-A', '10-B', '11-A', '11-B', '12-A', '12-B'];

// Mobile Fee Card Component
const FeeCard: React.FC<{ fee: any; onViewFee: (fee: any) => void; onPayment: (fee: any) => void; onSendReminder: (fee: any) => void; getStatusBadge: (status: string) => JSX.Element; getTypeLabel: (type: string) => string; user: any; isSubmitting: boolean }> = ({
  fee,
  onViewFee,
  onPayment,
  onSendReminder,
  getStatusBadge,
  getTypeLabel,
  user,
  isSubmitting
}) => (
  <Card className="mb-4">
    <CardContent className="p-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="h-6 w-6 text-primary" />
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="font-semibold text-base text-gray-900 truncate">
              {fee.studentName}
            </h3>
            <p className="text-sm text-gray-600">ID: {fee.studentId} • Class: {fee.class}</p>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onViewFee(fee)}>
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </DropdownMenuItem>
            {fee.status !== 'paid' && (
              <DropdownMenuItem onClick={() => onPayment(fee)}>
                <CreditCard className="h-4 w-4 mr-2" />
                Make Payment
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => onSendReminder(fee)}>
              <Send className="h-4 w-4 mr-2" />
              Send Reminder
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Fee Type:</span>
          <span className="font-medium text-sm capitalize">{getTypeLabel(fee.type)}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Amount:</span>
          <div className="text-right">
            <p className="font-medium text-sm">${fee.amount}</p>
            {fee.paidAmount > 0 && (
              <p className="text-xs text-gray-500">Paid: ${fee.paidAmount}</p>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Status:</span>
          {getStatusBadge(fee.status)}
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Due Date:</span>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3 text-gray-400" />
            <span className="text-sm">{new Date(fee.dueDate).toLocaleDateString()}</span>
          </div>
        </div>

        {fee.status === 'partial' && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-gray-600">Payment Progress:</span>
              <span className="text-xs text-gray-600">{((fee.paidAmount / fee.amount) * 100).toFixed(0)}%</span>
            </div>
            <Progress value={(fee.paidAmount / fee.amount) * 100} className="h-2" />
          </div>
        )}
      </div>

      <div className="mt-3 pt-3 border-t">
        <p className="text-xs text-gray-600">{fee.description}</p>
      </div>
    </CardContent>
  </Card>
);

export const Fees: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [fees, setFees] = useState(mockFeeData);
  const [isAddFeeOpen, setIsAddFeeOpen] = useState(false);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  const [isViewFeeOpen, setIsViewFeeOpen] = useState(false);
  const [selectedFee, setSelectedFee] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table');

  // Payment form state
  const [paymentForm, setPaymentForm] = useState({
    paymentMethod: '',
    amount: '',
    transactionId: '',
    notes: ''
  });

  // Add fee form state
  const [addFeeForm, setAddFeeForm] = useState({
    studentId: '',
    type: '',
    amount: '',
    dueDate: '',
    description: ''
  });

  // Student selection state
  const [studentSearch, setStudentSearch] = useState('');
  const [selectedClass, setSelectedClass] = useState('all');
  const [isStudentDropdownOpen, setIsStudentDropdownOpen] = useState(false);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><CheckCircle className="w-3 h-3 mr-1" />Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><AlertTriangle className="w-3 h-3 mr-1" />Overdue</Badge>;
      case 'partial':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100"><CreditCard className="w-3 h-3 mr-1" />Partial</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const filteredFees = fees.filter(fee => {
    const matchesSearch = fee.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || fee.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || fee.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Calculate statistics
  const totalFees = fees.reduce((sum, fee) => sum + fee.amount, 0);
  const totalPaid = fees.reduce((sum, fee) => sum + fee.paidAmount, 0);
  const totalPending = fees.filter(f => f.status === 'pending').reduce((sum, fee) => sum + fee.amount, 0);
  const totalOverdue = fees.filter(f => f.status === 'overdue').reduce((sum, fee) => sum + fee.amount, 0);
  const collectionRate = totalFees > 0 ? (totalPaid / totalFees * 100).toFixed(1) : '0';



  // Filter students based on search and class
  const filteredStudents = mockStudents.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(studentSearch.toLowerCase()) ||
                         student.id.toLowerCase().includes(studentSearch.toLowerCase()) ||
                         student.rollNumber.includes(studentSearch);
    const matchesClass = selectedClass === 'all' || student.class === selectedClass;
    return matchesSearch && matchesClass;
  });

  const handleAddFeeSubmit = async () => {
    // Validate form
    if (!addFeeForm.studentId || !addFeeForm.type || !addFeeForm.amount || !addFeeForm.dueDate) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (parseFloat(addFeeForm.amount) <= 0) {
      toast({
        title: "Error",
        description: "Amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const selectedStudent = mockStudents.find(s => s.id === addFeeForm.studentId);
      if (!selectedStudent) {
        throw new Error('Student not found');
      }

      const newFee = {
        id: (fees.length + 1).toString(),
        studentId: addFeeForm.studentId,
        studentName: selectedStudent.name,
        class: selectedStudent.class,
        type: addFeeForm.type,
        amount: parseFloat(addFeeForm.amount),
        dueDate: addFeeForm.dueDate,
        status: 'pending' as const,
        paidAmount: 0,
        description: addFeeForm.description || `${getTypeLabel(addFeeForm.type)} Fee`,
        createdAt: new Date().toISOString().split('T')[0]
      };

      setFees(prev => [newFee, ...prev]);
      setAddFeeForm({ studentId: '', type: '', amount: '', dueDate: '', description: '' });
      setStudentSearch('');
      setSelectedClass('all');
      setIsAddFeeOpen(false);

      toast({
        title: "Fee Added Successfully!",
        description: `${getTypeLabel(addFeeForm.type)} fee of $${addFeeForm.amount} added for ${selectedStudent.name}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add fee. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetAddFeeForm = () => {
    setAddFeeForm({ studentId: '', type: '', amount: '', dueDate: '', description: '' });
    setStudentSearch('');
    setSelectedClass('all');
  };

  const clearFilters = () => {
    setSelectedType('all');
    setSelectedStatus('all');
    setSearchTerm('');
    toast({
      title: "Filters Cleared",
      description: "All filters have been reset.",
    });
  };

  const handleViewFee = (fee: any) => {
    setSelectedFee(fee);
    setIsViewFeeOpen(true);
  };

  const handlePayment = (fee: any) => {
    setSelectedFee(fee);
    setPaymentForm({
      paymentMethod: '',
      amount: (fee.amount - fee.paidAmount).toString(),
      transactionId: '',
      notes: ''
    });
    setIsPaymentOpen(true);
  };

  const handleSendReminder = async (fee: any) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Reminder Sent!",
        description: `Payment reminder sent to ${fee.studentName} for ${getTypeLabel(fee.type)} fee.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send reminder. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentSubmit = async () => {
    if (!paymentForm.paymentMethod || !paymentForm.amount) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const paymentAmount = parseFloat(paymentForm.amount);
    if (paymentAmount <= 0 || paymentAmount > (selectedFee.amount - selectedFee.paidAmount)) {
      toast({
        title: "Error",
        description: "Invalid payment amount",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newPaidAmount = selectedFee.paidAmount + paymentAmount;
      const newStatus = newPaidAmount >= selectedFee.amount ? 'paid' : 'partial';

      setFees(prev => prev.map(fee =>
        fee.id === selectedFee.id
          ? {
              ...fee,
              status: newStatus,
              paidAmount: newPaidAmount,
              paidDate: new Date().toISOString().split('T')[0],
              paymentMethod: paymentForm.paymentMethod,
              transactionId: paymentForm.transactionId || `TXN${Date.now()}`,
              paymentNotes: paymentForm.notes
            }
          : fee
      ));

      setIsPaymentOpen(false);
      setSelectedFee(null);
      setPaymentForm({ paymentMethod: '', amount: '', transactionId: '', notes: '' });

      toast({
        title: "Payment Processed!",
        description: `Payment of $${paymentAmount} processed successfully for ${selectedFee.studentName}.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Fee Management</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            Track and manage student fee payments and collections
          </p>
        </div>
        <div className="flex gap-2">
          {user?.role === 'admin' && (
            <Dialog open={isAddFeeOpen} onOpenChange={setIsAddFeeOpen}>
              <DialogTrigger asChild>
                <Button className="text-xs md:text-sm px-3 py-2 h-9">
                  <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                  <span className="hidden sm:inline">Add Fee</span>
                  <span className="sm:hidden">Add</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Add New Fee</DialogTitle>
                  <DialogDescription>
                    Create a new fee entry for a student
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-6">
                  {/* Student Selection with Search and Class Filter */}
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">Student Selection</Label>

                    {/* Class Filter and Search */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Filter by Class</Label>
                        <Select value={selectedClass} onValueChange={setSelectedClass}>
                          <SelectTrigger>
                            <SelectValue placeholder="All classes" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Classes</SelectItem>
                            {classes.map(cls => (
                              <SelectItem key={cls} value={cls}>{cls}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Search Students</Label>
                        <div className="relative">
                          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Search by name, ID, or roll number..."
                            value={studentSearch}
                            onChange={(e) => setStudentSearch(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Student Selection */}
                    <div className="space-y-2">
                      <Label>Select Student *</Label>
                      <Select value={addFeeForm.studentId} onValueChange={(value) => setAddFeeForm(prev => ({ ...prev, studentId: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a student" />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {filteredStudents.length === 0 ? (
                            <div className="p-4 text-center text-gray-500">
                              <p>No students found</p>
                              <p className="text-xs">Try adjusting your search or class filter</p>
                            </div>
                          ) : (
                            filteredStudents.map(student => (
                              <SelectItem key={student.id} value={student.id}>
                                <div className="flex items-center justify-between w-full">
                                  <span>{student.name}</span>
                                  <span className="text-xs text-gray-500 ml-2">
                                    {student.id} • {student.class} • Roll: {student.rollNumber}
                                  </span>
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-500">
                        Showing {filteredStudents.length} of {mockStudents.length} students
                      </p>
                    </div>
                  </div>

                  {/* Fee Details */}
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">Fee Details</Label>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Fee Type *</Label>
                        <Select value={addFeeForm.type} onValueChange={(value) => setAddFeeForm(prev => ({ ...prev, type: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select fee type" />
                          </SelectTrigger>
                          <SelectContent>
                            {feeTypes.map(type => (
                              <SelectItem key={type} value={type} className="capitalize">{getTypeLabel(type)}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Amount *</Label>
                        <Input
                          type="number"
                          placeholder="1500"
                          min="0"
                          step="0.01"
                          value={addFeeForm.amount}
                          onChange={(e) => setAddFeeForm(prev => ({ ...prev, amount: e.target.value }))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Due Date *</Label>
                      <Input
                        type="date"
                        value={addFeeForm.dueDate}
                        onChange={(e) => setAddFeeForm(prev => ({ ...prev, dueDate: e.target.value }))}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Description</Label>
                      <Input
                        placeholder="e.g., Term 2 Tuition Fee"
                        value={addFeeForm.description}
                        onChange={(e) => setAddFeeForm(prev => ({ ...prev, description: e.target.value }))}
                      />
                      <p className="text-xs text-gray-500">Optional - will auto-generate if left empty</p>
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      className="flex-1"
                      onClick={handleAddFeeSubmit}
                      disabled={isSubmitting}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {isSubmitting ? 'Adding Fee...' : 'Add Fee'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddFeeOpen(false);
                        resetAddFeeForm();
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
          <Button variant="outline" className="text-xs md:text-sm px-3 py-2 h-9">
            <Download className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Export</span>
            <span className="sm:hidden">Export</span>
          </Button>
        </div>
      </div>

      {/* Fee Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <MetricCard
          title="Total Fees"
          value={`$${totalFees.toLocaleString()}`}
          icon={<DollarSign className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Collected"
          value={`$${totalPaid.toLocaleString()}`}
          icon={<CheckCircle className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Pending"
          value={`$${totalPending.toLocaleString()}`}
          icon={<Clock className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Collection Rate"
          value={`${collectionRate}%`}
          icon={<CreditCard className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-purple-500"
        />
      </div>

      {/* Collection Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">Fee Collection Progress</CardTitle>
          <CardDescription className="text-xs sm:text-sm">
            Overall fee collection status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Total Collection Progress</span>
                <span className="text-sm text-gray-600">{collectionRate}%</span>
              </div>
              <Progress value={parseFloat(collectionRate)} className="h-3" />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
              <div className="flex justify-between">
                <span>Total Fees:</span>
                <span className="font-medium">${totalFees.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Collected:</span>
                <span className="font-medium text-green-600">${totalPaid.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Outstanding:</span>
                <span className="font-medium text-red-600">${(totalFees - totalPaid).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="all" className="text-xs sm:text-sm">
            {isMobile ? "All" : "All Fees"}
          </TabsTrigger>
          <TabsTrigger value="pending" className="text-xs sm:text-sm">
            {isMobile ? "Pending" : "Pending"}
          </TabsTrigger>
          <TabsTrigger value="overdue" className="text-xs sm:text-sm">
            {isMobile ? "Overdue" : "Overdue"}
          </TabsTrigger>
          <TabsTrigger value="paid" className="text-xs sm:text-sm">
            {isMobile ? "Paid" : "Paid"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Fee Filters</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Filter fees by type, status, or search for specific students
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label>Fee Type</Label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {feeTypes.map(type => (
                        <SelectItem key={type} value={type} className="capitalize">{getTypeLabel(type)}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="partial">Partial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search students..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>&nbsp;</Label>
                  <Button className="w-full" variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fees List */}
          {isMobile && viewMode === 'cards' ? (
            // Mobile Card View
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Fee Records ({filteredFees.length})</h2>
              </div>
              {filteredFees.length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center text-gray-500">
                      No fees found matching your criteria.
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {filteredFees.map((fee) => (
                    <FeeCard
                      key={fee.id}
                      fee={fee}
                      onViewFee={handleViewFee}
                      onPayment={handlePayment}
                      onSendReminder={handleSendReminder}
                      getStatusBadge={getStatusBadge}
                      getTypeLabel={getTypeLabel}
                      user={user}
                      isSubmitting={isSubmitting}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            // Table View (Desktop and Mobile Table Mode)
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Fee Records</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Manage all student fee payments and collections
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-[700px]">
                    <TableHeader>
                      <TableRow className="bg-muted/30">
                        <TableHead className="text-xs sm:text-sm w-32">Student</TableHead>
                        <TableHead className="text-xs sm:text-sm w-32">Fee Type</TableHead>
                        <TableHead className="text-xs sm:text-sm w-24">Amount</TableHead>
                        <TableHead className="text-xs sm:text-sm w-24 hidden lg:table-cell">Due Date</TableHead>
                        <TableHead className="text-xs sm:text-sm w-20">Status</TableHead>
                        <TableHead className="text-xs sm:text-sm w-24">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFees.map((fee) => (
                        <TableRow key={fee.id}>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4">
                            <div>
                              <p className="font-medium text-xs sm:text-sm">{fee.studentName}</p>
                              <p className="text-xs text-gray-500">{fee.studentId} • {fee.class}</p>
                            </div>
                          </TableCell>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4">
                            <div>
                              <p className="font-medium capitalize text-xs sm:text-sm">{getTypeLabel(fee.type)}</p>
                              <p className="text-xs text-gray-500 truncate max-w-32">{fee.description}</p>
                            </div>
                          </TableCell>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4">
                            <div>
                              <p className="font-medium text-xs sm:text-sm">${fee.amount}</p>
                              {fee.paidAmount > 0 && (
                                <p className="text-xs text-green-600">Paid: ${fee.paidAmount}</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4 hidden lg:table-cell">
                            <div>
                              <p className="font-medium text-xs sm:text-sm">{new Date(fee.dueDate).toLocaleDateString()}</p>
                              {fee.status === 'overdue' && (
                                <p className="text-xs text-red-600">Overdue</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4">{getStatusBadge(fee.status)}</TableCell>
                          <TableCell className="py-2 px-2 sm:py-3 sm:px-4">
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewFee(fee)}
                                title="View Details"
                                className="h-7 w-7 p-0"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                              {fee.status !== 'paid' && (user?.role === 'parent' || user?.role === 'admin') && (
                                <Button
                                  size="sm"
                                  onClick={() => handlePayment(fee)}
                                  title="Make Payment"
                                  className="bg-green-600 hover:bg-green-700 text-white h-7 w-7 p-0"
                                >
                                  <CreditCard className="h-3 w-3" />
                                </Button>
                              )}
                              {user?.role === 'admin' && fee.status !== 'paid' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleSendReminder(fee)}
                                  disabled={isSubmitting}
                                  title="Send Payment Reminder"
                                  className="text-blue-600 hover:text-blue-800 h-7 w-7 p-0"
                                >
                                  <Send className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {filteredFees.length === 0 && (
                    <div className="text-center py-8 text-gray-500 px-6 text-xs sm:text-base">
                      No fees found matching your criteria.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Pending Fees Tab */}
        <TabsContent value="pending">
          {isMobile && viewMode === 'cards' ? (
            // Mobile Card View for Pending
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Pending Payments ({filteredFees.filter(fee => fee.status === 'pending').length})</h2>
              </div>
              {filteredFees.filter(fee => fee.status === 'pending').length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center">
                      <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No pending payments</h3>
                      <p className="text-gray-600">All fees are up to date!</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {filteredFees.filter(fee => fee.status === 'pending').map((fee) => (
                    <FeeCard
                      key={fee.id}
                      fee={fee}
                      onViewFee={handleViewFee}
                      onPayment={handlePayment}
                      onSendReminder={handleSendReminder}
                      getStatusBadge={getStatusBadge}
                      getTypeLabel={getTypeLabel}
                      user={user}
                      isSubmitting={isSubmitting}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            // Table View for Pending
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Pending Payments</CardTitle>
                <CardDescription className="text-xs sm:text-sm">Fees that are due but not yet paid</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table className="min-w-[600px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Fee Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFees.filter(fee => fee.status === 'pending').map((fee) => (
                    <TableRow key={fee.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{fee.studentName}</p>
                          <p className="text-sm text-gray-500">{fee.class}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium capitalize">{getTypeLabel(fee.type)}</p>
                          <p className="text-sm text-gray-500">{fee.description}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">${fee.amount}</p>
                          {fee.paidAmount > 0 && (
                            <p className="text-sm text-green-600">Paid: ${fee.paidAmount}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{new Date(fee.dueDate).toLocaleDateString()}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(fee.dueDate) < new Date() ? 'Overdue' : 'Due soon'}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(fee.status)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewFee(fee)}
                            title="View Details"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          {(user?.role === 'parent' || user?.role === 'admin') && (
                            <Button
                              size="sm"
                              onClick={() => handlePayment(fee)}
                              title="Make Payment"
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              <CreditCard className="h-3 w-3" />
                            </Button>
                          )}
                          {user?.role === 'admin' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSendReminder(fee)}
                              disabled={isSubmitting}
                              title="Send Payment Reminder"
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <Send className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                  </Table>
                  {filteredFees.filter(fee => fee.status === 'pending').length === 0 && (
                    <div className="text-center py-8">
                      <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No pending payments</h3>
                      <p className="text-gray-600">All fees are up to date!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Overdue Fees Tab */}
        <TabsContent value="overdue">
          {isMobile && viewMode === 'cards' ? (
            // Mobile Card View for Overdue
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Overdue Payments ({filteredFees.filter(fee => fee.status === 'overdue' || (fee.status === 'pending' && new Date(fee.dueDate) < new Date())).length})</h2>
              </div>
              {filteredFees.filter(fee => fee.status === 'overdue' || (fee.status === 'pending' && new Date(fee.dueDate) < new Date())).length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center">
                      <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No overdue payments</h3>
                      <p className="text-gray-600">Great! All payments are on time.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {filteredFees.filter(fee => fee.status === 'overdue' || (fee.status === 'pending' && new Date(fee.dueDate) < new Date())).map((fee) => (
                    <FeeCard
                      key={fee.id}
                      fee={fee}
                      onViewFee={handleViewFee}
                      onPayment={handlePayment}
                      onSendReminder={handleSendReminder}
                      getStatusBadge={getStatusBadge}
                      getTypeLabel={getTypeLabel}
                      user={user}
                      isSubmitting={isSubmitting}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            // Table View for Overdue
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Overdue Payments</CardTitle>
                <CardDescription className="text-xs sm:text-sm">Fees that are past their due date</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table className="min-w-[700px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Fee Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Days Overdue</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFees.filter(fee => fee.status === 'overdue' || (fee.status === 'pending' && new Date(fee.dueDate) < new Date())).map((fee) => {
                    const daysOverdue = Math.floor((new Date().getTime() - new Date(fee.dueDate).getTime()) / (1000 * 3600 * 24));
                    return (
                      <TableRow key={fee.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{fee.studentName}</p>
                            <p className="text-sm text-gray-500">{fee.class}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium capitalize">{getTypeLabel(fee.type)}</p>
                            <p className="text-sm text-gray-500">{fee.description}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium text-red-600">${fee.amount}</p>
                            {fee.paidAmount > 0 && (
                              <p className="text-sm text-green-600">Paid: ${fee.paidAmount}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="font-medium text-red-600">{new Date(fee.dueDate).toLocaleDateString()}</p>
                        </TableCell>
                        <TableCell>
                          <Badge variant="destructive">{daysOverdue} days</Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge('overdue')}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewFee(fee)}
                              title="View Details"
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            {(user?.role === 'parent' || user?.role === 'admin') && (
                              <Button
                                size="sm"
                                onClick={() => handlePayment(fee)}
                                title="Make Payment"
                                className="bg-red-600 hover:bg-red-700 text-white"
                              >
                                <CreditCard className="h-3 w-3" />
                              </Button>
                            )}
                            {user?.role === 'admin' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleSendReminder(fee)}
                                disabled={isSubmitting}
                                title="Send Urgent Reminder"
                                className="text-red-600 hover:text-red-800"
                              >
                                <Send className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
                  </Table>
                  {filteredFees.filter(fee => fee.status === 'overdue' || (fee.status === 'pending' && new Date(fee.dueDate) < new Date())).length === 0 && (
                    <div className="text-center py-8">
                      <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No overdue payments</h3>
                      <p className="text-gray-600">Great! All payments are on time.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Paid Fees Tab */}
        <TabsContent value="paid">
          {isMobile && viewMode === 'cards' ? (
            // Mobile Card View for Paid
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Paid Fees ({filteredFees.filter(fee => fee.status === 'paid').length})</h2>
              </div>
              {filteredFees.filter(fee => fee.status === 'paid').length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center">
                      <Receipt className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No paid fees</h3>
                      <p className="text-gray-600">No payments have been completed yet.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {filteredFees.filter(fee => fee.status === 'paid').map((fee) => (
                    <FeeCard
                      key={fee.id}
                      fee={fee}
                      onViewFee={handleViewFee}
                      onPayment={handlePayment}
                      onSendReminder={handleSendReminder}
                      getStatusBadge={getStatusBadge}
                      getTypeLabel={getTypeLabel}
                      user={user}
                      isSubmitting={isSubmitting}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            // Table View for Paid
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Paid Fees</CardTitle>
                <CardDescription className="text-xs sm:text-sm">Successfully completed payments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table className="min-w-[700px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Fee Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Payment Date</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFees.filter(fee => fee.status === 'paid').map((fee) => (
                    <TableRow key={fee.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{fee.studentName}</p>
                          <p className="text-sm text-gray-500">{fee.class}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium capitalize">{getTypeLabel(fee.type)}</p>
                          <p className="text-sm text-gray-500">{fee.description}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-green-600">${fee.amount}</p>
                          <p className="text-sm text-gray-500">Fully paid</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="font-medium">{fee.paidDate ? new Date(fee.paidDate).toLocaleDateString() : 'N/A'}</p>
                      </TableCell>
                      <TableCell>
                        <p className="font-medium">{fee.paymentMethod || 'N/A'}</p>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewFee(fee)}
                          title="View Details"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                  </Table>
                  {filteredFees.filter(fee => fee.status === 'paid').length === 0 && (
                    <div className="text-center py-8">
                      <DollarSign className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No paid fees</h3>
                      <p className="text-gray-600">No payments have been completed yet.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Payment Dialog */}
      <Dialog open={isPaymentOpen} onOpenChange={setIsPaymentOpen}>
        <DialogContent className="max-w-2xl sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <CreditCard className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
              Make Payment
            </DialogTitle>
            <DialogDescription className="text-sm">
              Process payment for {selectedFee?.description}
            </DialogDescription>
          </DialogHeader>

          {selectedFee && (
            <div className="flex-1 overflow-y-auto pr-2">
              <div className="space-y-4">
                {/* Payment Summary Card */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg text-gray-900">Payment Summary</h3>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">${selectedFee.amount - selectedFee.paidAmount}</div>
                      <div className="text-sm text-gray-600">Amount Due</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 block">Student</span>
                      <span className="font-medium">{selectedFee.studentName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Class</span>
                      <span className="font-medium">{selectedFee.class}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Fee Type</span>
                      <span className="font-medium">{getTypeLabel(selectedFee.type)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Due Date</span>
                      <span className="font-medium">{new Date(selectedFee.dueDate).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Payment Breakdown */}
                  <div className="mt-4 pt-3 border-t border-green-200">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">${selectedFee.amount}</div>
                        <div className="text-gray-600">Total Fee</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">${selectedFee.paidAmount}</div>
                        <div className="text-gray-600">Already Paid</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-red-600">${selectedFee.amount - selectedFee.paidAmount}</div>
                        <div className="text-gray-600">Remaining</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Form */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Payment Method */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Payment Method</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Select
                        value={paymentForm.paymentMethod}
                        onValueChange={(value) => setPaymentForm(prev => ({ ...prev, paymentMethod: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                        <SelectContent>
                          {paymentMethods.map(method => (
                            <SelectItem key={method} value={method}>
                              <div className="flex items-center gap-2">
                                {method === 'Cash' && <DollarSign className="h-4 w-4" />}
                                {method === 'Credit Card' && <CreditCard className="h-4 w-4" />}
                                {method === 'Debit Card' && <CreditCard className="h-4 w-4" />}
                                {method === 'Bank Transfer' && <Building className="h-4 w-4" />}
                                {method === 'Online Payment' && <Globe className="h-4 w-4" />}
                                {method}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </CardContent>
                  </Card>

                  {/* Payment Amount */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Payment Amount</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          type="number"
                          placeholder="0.00"
                          value={paymentForm.amount}
                          onChange={(e) => setPaymentForm(prev => ({ ...prev, amount: e.target.value }))}
                          max={selectedFee.amount - selectedFee.paidAmount}
                          min="0.01"
                          step="0.01"
                          className="pl-10 text-lg font-semibold"
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setPaymentForm(prev => ({ ...prev, amount: (selectedFee.amount - selectedFee.paidAmount).toString() }))}
                          className="text-xs"
                        >
                          Pay Full Amount
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setPaymentForm(prev => ({ ...prev, amount: Math.round((selectedFee.amount - selectedFee.paidAmount) / 2).toString() }))}
                          className="text-xs"
                        >
                          Pay Half
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">
                        Maximum: ${selectedFee.amount - selectedFee.paidAmount}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Additional Details */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Additional Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Transaction ID (Optional)</Label>
                      <Input
                        placeholder="Enter transaction ID or reference number"
                        value={paymentForm.transactionId}
                        onChange={(e) => setPaymentForm(prev => ({ ...prev, transactionId: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Payment Notes (Optional)</Label>
                      <Input
                        placeholder="Add any notes about this payment"
                        value={paymentForm.notes}
                        onChange={(e) => setPaymentForm(prev => ({ ...prev, notes: e.target.value }))}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Preview */}
                {paymentForm.amount && parseFloat(paymentForm.amount) > 0 && (
                  <Card className="bg-blue-50 border-blue-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base text-blue-800">Payment Preview</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Payment Amount:</span>
                          <span className="font-semibold">${paymentForm.amount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Remaining After Payment:</span>
                          <span className="font-semibold">
                            ${Math.max(0, selectedFee.amount - selectedFee.paidAmount - parseFloat(paymentForm.amount || '0')).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>New Status:</span>
                          <span className="font-semibold">
                            {(selectedFee.paidAmount + parseFloat(paymentForm.amount || '0')) >= selectedFee.amount ? 'Paid' : 'Partial'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {/* Fixed Footer */}
          <div className="flex-shrink-0 border-t pt-4 mt-4">
            <div className="flex gap-2">
              <Button
                className="flex-1 bg-green-600 hover:bg-green-700"
                onClick={handlePaymentSubmit}
                disabled={isSubmitting || !paymentForm.paymentMethod || !paymentForm.amount || parseFloat(paymentForm.amount || '0') <= 0}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Processing Payment...' : `Process Payment ($${paymentForm.amount || '0'})`}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsPaymentOpen(false);
                  setPaymentForm({ paymentMethod: '', amount: '', transactionId: '', notes: '' });
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* View Fee Dialog */}
      <Dialog open={isViewFeeOpen} onOpenChange={setIsViewFeeOpen}>
        <DialogContent className="max-w-2xl max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Fee Details</DialogTitle>
            <DialogDescription>
              Complete information about this fee record
            </DialogDescription>
          </DialogHeader>

          {selectedFee && (
            <div className="flex-1 overflow-y-auto pr-2">
              <div className="space-y-4">
                {/* Summary Card */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-lg text-gray-900">{selectedFee.studentName}</h3>
                    <div className="text-right">
                      {getStatusBadge(selectedFee.status)}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 block">Class</span>
                      <span className="font-medium">{selectedFee.class}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Fee Type</span>
                      <span className="font-medium">{getTypeLabel(selectedFee.type)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Total Amount</span>
                      <span className="font-bold text-lg text-blue-600">${selectedFee.amount}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 block">Remaining</span>
                      <span className="font-bold text-lg text-red-600">${selectedFee.amount - selectedFee.paidAmount}</span>
                    </div>
                  </div>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Student Details */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Student Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Student ID:</span>
                        <span className="font-medium">{selectedFee.studentId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Class:</span>
                        <span className="font-medium">{selectedFee.class}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Name:</span>
                        <span className="font-medium">{selectedFee.studentName}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Fee Details */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Fee Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type:</span>
                        <span className="font-medium">{getTypeLabel(selectedFee.type)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Due Date:</span>
                        <span className="font-medium">{new Date(selectedFee.dueDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Created:</span>
                        <span className="font-medium">{new Date(selectedFee.createdAt).toLocaleDateString()}</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Description */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700">{selectedFee.description}</p>
                  </CardContent>
                </Card>

                {/* Payment Information */}
                {(selectedFee.paidAmount > 0 || selectedFee.paymentMethod || selectedFee.transactionId || selectedFee.paymentNotes) && (
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Payment History</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Paid Amount:</span>
                          <span className="font-medium text-green-600">${selectedFee.paidAmount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Remaining:</span>
                          <span className="font-medium text-red-600">${selectedFee.amount - selectedFee.paidAmount}</span>
                        </div>
                      </div>

                      {selectedFee.paidDate && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Payment Date:</span>
                          <span className="font-medium">{new Date(selectedFee.paidDate).toLocaleDateString()}</span>
                        </div>
                      )}

                      {selectedFee.paymentMethod && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Payment Method:</span>
                          <span className="font-medium">{selectedFee.paymentMethod}</span>
                        </div>
                      )}

                      {selectedFee.transactionId && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Transaction ID:</span>
                          <span className="font-medium font-mono text-sm">{selectedFee.transactionId}</span>
                        </div>
                      )}

                      {selectedFee.paymentNotes && (
                        <div>
                          <span className="text-gray-600 block mb-1">Payment Notes:</span>
                          <p className="font-medium text-sm bg-gray-50 p-2 rounded">{selectedFee.paymentNotes}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {/* Fixed Footer */}
          <div className="flex-shrink-0 border-t pt-4 mt-4">
            <div className="flex gap-2">
              {selectedFee && selectedFee.status !== 'paid' && (user?.role === 'parent' || user?.role === 'admin') && (
                <Button
                  className="flex-1"
                  onClick={() => {
                    setIsViewFeeOpen(false);
                    handlePayment(selectedFee);
                  }}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Make Payment
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => setIsViewFeeOpen(false)}
                className={!selectedFee || selectedFee.status === 'paid' || (user?.role !== 'parent' && user?.role !== 'admin') ? 'flex-1' : ''}
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
