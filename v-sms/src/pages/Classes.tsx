
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { Search, Plus, Calendar, Edit, Trash2, Eye, Loader2, Grid, List, MoreVertical, Clock, GraduationCap } from "lucide-react";
import { MetricCard } from "@/components/MetricCard";
import { Book, Users, User } from "lucide-react";
import { ClassForm } from "@/components/forms/ClassForm";
import { classesService, getCurrentSchoolId } from "@/services/supabaseService";
import { Class } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { useDataAccess } from "@/hooks/useDataAccess.tsx";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export const Classes = () => {
  const { schoolSlug } = useParams<{ schoolSlug: string }>();
  const [searchTerm, setSearchTerm] = useState("");
  const [classes, setClasses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<any | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<'table' | 'cards'>(isMobile ? 'cards' : 'table');
  const { canCreate, canEdit, canDelete } = useDataAccess('classes');

  // Load school ID and classes
  useEffect(() => {
    const initializeData = async () => {
      if (!schoolSlug) {
        console.log('🏫 Classes: No schoolSlug provided');
        return;
      }

      try {
        console.log('🏫 Classes: Initializing data for school:', schoolSlug);
        const currentSchoolId = await getCurrentSchoolId(schoolSlug);
        console.log('🏫 Classes: Got school ID:', currentSchoolId);

        if (currentSchoolId) {
          setSchoolId(currentSchoolId);
          console.log('🏫 Classes: Loading classes...');
          await loadClasses(currentSchoolId);
          console.log('🏫 Classes: Data loading completed');
        } else {
          console.error('🏫 Classes: No school ID found for slug:', schoolSlug);
        }
      } catch (error) {
        console.error('🏫 Classes: Error getting school ID:', error);
        toast({
          title: "Error",
          description: "Failed to load school information",
          variant: "destructive",
        });
      }
    };

    initializeData();
  }, [schoolSlug]);

  // Auto-switch to cards view on mobile
  useEffect(() => {
    if (isMobile && viewMode === 'table') {
      setViewMode('cards');
    }
  }, [isMobile, viewMode]);

  const loadClasses = async (currentSchoolId?: string) => {
    const schoolIdToUse = currentSchoolId || schoolId;
    if (!schoolIdToUse) return;

    setIsLoading(true);
    try {
      console.log('🏫 Classes: Loading classes for school ID:', schoolIdToUse);
      const classesData = await classesService.getAll(schoolIdToUse);
      console.log('🏫 Classes: Raw classes data:', classesData);
      console.log('🏫 Classes: Number of classes found:', classesData?.length || 0);

      // Log each class for debugging
      classesData?.forEach((cls: any, index: number) => {
        console.log(`🏫 Class ${index + 1}:`, {
          id: cls.id,
          name: cls.name,
          section: cls.section,
          grade_level: cls.grade_level,
          max_students: cls.max_students,
          is_active: cls.is_active
        });
      });

      // Use the raw data directly with original field names
      setClasses(classesData || []);
      console.log('🏫 Classes: Set classes state with', classesData?.length || 0, 'classes');
    } catch (error) {
      console.error('Failed to load classes:', error);
      toast({
        title: "Error",
        description: "Failed to load classes. Please try again.",
        variant: "destructive",
      });
      setClasses([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddClass = async (data: any) => {
    if (!schoolId) return;

    setIsSubmitting(true);
    try {
      const classData = {
        name: data.name,
        grade_level: data.grade,
        section: null, // Section field removed from form
        academic_year: data.academicYear || '2024-2025',
        max_students: data.capacity || 30,
        class_teacher_id: null, // Teacher assignment removed for now
      };

      await classesService.create(classData, schoolId);

      // Reload classes to get the latest data
      await loadClasses();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Class created successfully",
      });
    } catch (error: any) {
      console.error('Failed to create class:', error);

      let errorMessage = "Failed to create class. Please try again.";

      if (error?.message?.includes('Row Level Security')) {
        errorMessage = "Permission denied: Database security policy prevents class creation. Please contact your administrator.";
      } else if (error?.code === '23505') {
        errorMessage = "A class with this name or grade/section combination already exists.";
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditClass = async (data: any) => {
    if (!selectedClass) return;

    setIsSubmitting(true);
    try {
      const updateData = {
        name: data.name,
        grade_level: data.grade,
        section: null, // Section field removed from form
        academic_year: data.academicYear,
        max_students: data.capacity,
        class_teacher_id: null, // Teacher assignment removed for now
      };

      await classesService.update(selectedClass.id, updateData);

      // Reload classes to get the latest data
      await loadClasses();
      setIsEditDialogOpen(false);
      setSelectedClass(null);
      toast({
        title: "Success",
        description: "Class updated successfully",
      });
    } catch (error: any) {
      console.error('Failed to update class:', error);

      let errorMessage = "Failed to update class. Please try again.";

      if (error?.code === '23505') {
        errorMessage = "A class with this name or grade/section combination already exists.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClass = async () => {
    if (!selectedClass) return;

    setIsSubmitting(true);
    try {
      await classesService.delete(selectedClass.id);

      // Reload classes to get the latest data
      await loadClasses();
      setIsDeleteDialogOpen(false);
      setSelectedClass(null);
      toast({
        title: "Success",
        description: "Class deleted successfully",
      });
    } catch (error: any) {
      console.error('Failed to delete class:', error);
      toast({
        title: "Error",
        description: "Failed to delete class. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (classItem: Class) => {
    setSelectedClass(classItem);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (classItem: Class) => {
    setSelectedClass(classItem);
    setIsDeleteDialogOpen(true);
  };

  const openViewDialog = (classItem: Class) => {
    setSelectedClass(classItem);
    setIsViewDialogOpen(true);
  };

  const filteredClasses = classes.filter(classItem =>
    (classItem.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (classItem.grade?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (classItem.section?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Mobile Card Component
  const ClassCard: React.FC<{ classItem: Class }> = ({ classItem }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-lg">📚</span>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base text-gray-900 truncate">
                {classItem.name} {classItem.section}
              </h3>
              <p className="text-sm text-gray-600">Grade Level: {classItem.grade_level}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => openViewDialog(classItem)}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => openEditDialog(classItem)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Class
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => openDeleteDialog(classItem)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Class
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
          <Users className="h-4 w-4 text-gray-400" />
          <span>{classItem.current_students || 0}/{classItem.max_students || 0} students</span>
        </div>

        <div className="space-y-2 mb-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span>{classItem.academic_year || '2024-25'}</span>
          </div>
        </div>

        {classItem.description && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">Description:</span>
            </div>
            <p className="text-sm text-gray-600">{classItem.description}</p>
          </div>
        )}

        <div className="mt-3 pt-3 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <span className="font-medium">{Math.round((classItem.currentStrength / classItem.capacity) * 100)}%</span> full
            </div>
            <div className="w-20 bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full"
                style={{ width: `${Math.min((classItem.currentStrength / classItem.capacity) * 100, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Class Management</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">Manage and track class information</p>
        </div>
        <div className="flex items-center gap-2">
          {/* View Toggle - Only show on mobile */}
          <div className="md:hidden flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-8 px-3"
            >
              <Grid className="h-4 w-4" />
            </Button>
          </div>
          {canCreate() && (
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="bg-primary hover:bg-primary/90 text-xs md:text-sm px-3 py-2 h-9"
            >
              <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Add New Class</span>
              <span className="sm:hidden">Add</span>
            </Button>
          )}
        </div>
      </div>

      {/* Class Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-6">
        <MetricCard
          title="Total Classes"
          value={classes.length.toString()}
          icon={<Book className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="Total Students"
          value={classes.reduce((sum, c) => sum + c.currentStrength, 0).toString()}
          icon={<Users className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Average Class Size"
          value={classes.length > 0 ? Math.round(classes.reduce((sum, c) => sum + c.currentStrength, 0) / classes.length).toString() : '0'}
          icon={<User className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-purple-500"
        />
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-4 sm:pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search classes by name, grade, or section..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 text-sm sm:text-base py-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* Classes List */}
      {isMobile && viewMode === 'cards' ? (
        // Mobile Card View
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Classes ({filteredClasses.length})</h2>
          </div>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading classes...</span>
            </div>
          ) : filteredClasses.length === 0 ? (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-gray-500">
                  No classes found matching your search criteria.
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredClasses.map((classItem) => (
                <ClassCard key={classItem.id} classItem={classItem} />
              ))}
            </div>
          )}
        </div>
      ) : (
        // Table View (Desktop and Mobile Table Mode)
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Classes List</CardTitle>
            <CardDescription className="text-xs sm:text-sm">View and manage all classes</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading classes...</span>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full min-w-[700px] sm:min-w-[900px] table-auto">
                  <thead>
                    <tr className="border-b bg-muted/30">
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-48">CLASS</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-24">STUDENTS</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-32 hidden md:table-cell">SUBJECTS</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-24 hidden lg:table-cell">SCHEDULE</th>
                      <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm w-32">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredClasses.map((classItem) => (
                      <tr key={classItem.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                              <span className="text-sm sm:text-lg">📚</span>
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-xs sm:text-base truncate">{classItem.name} {classItem.section}</div>
                              <div className="text-xs sm:text-sm text-gray-500">Grade Level: {classItem.grade_level}</div>
                            </div>
                          </div>
                        </td>

                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div>
                            <div className="font-medium text-xs sm:text-sm">{classItem.current_students || 0}/{classItem.max_students || 0}</div>
                            <div className="text-xs text-gray-500">
                              Students
                            </div>
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 hidden md:table-cell">
                          <div className="flex flex-wrap gap-1">
                            <Badge variant="outline" className="text-xs">
                              {classItem.academic_year || '2024-25'}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4 hidden lg:table-cell">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-xs sm:text-sm">{classItem.academicYear}</span>
                          </div>
                        </td>
                        <td className="py-2 px-2 sm:py-3 sm:px-4">
                          <div className="flex gap-1 sm:gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openViewDialog(classItem)}
                              className="text-gray-600 hover:text-gray-800 p-1 h-8 w-8"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(classItem)}
                              className="text-blue-600 hover:text-blue-800 p-1 h-8 w-8"
                              title="Edit Class"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openDeleteDialog(classItem)}
                              className="text-red-600 hover:text-red-800 p-1 h-8 w-8"
                              title="Delete Class"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
              </table>
                  {filteredClasses.length === 0 && !isLoading && (
                    <div className="text-center py-8 text-gray-500 px-6 text-xs sm:text-base">
                      No classes found matching your search criteria.
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}

      {/* Add Class Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto sm:max-w-[95vw] sm:mx-4">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Add New Class</DialogTitle>
            <DialogDescription className="text-sm">
              Create a new class with all necessary information.
            </DialogDescription>
          </DialogHeader>
          <ClassForm
            onSubmit={handleAddClass}
            onCancel={() => setIsAddDialogOpen(false)}
            isLoading={isSubmitting}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Class Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto sm:max-w-[95vw] sm:mx-4">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Edit Class</DialogTitle>
            <DialogDescription className="text-sm">
              Update the class information below.
            </DialogDescription>
          </DialogHeader>
          {selectedClass && (
            <ClassForm
              class={selectedClass}
              onSubmit={handleEditClass}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedClass(null);
              }}
              isLoading={isSubmitting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Class Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl sm:max-w-[95vw] sm:mx-4 max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Class Details</DialogTitle>
            <DialogDescription className="text-sm">
              View complete class information
            </DialogDescription>
          </DialogHeader>
          {selectedClass && (
            <div className="space-y-4">
              {/* Class Header with Icon */}
              <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-2xl">📚</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {selectedClass.name}
                  </h3>
                  <p className="text-sm text-gray-600">Grade {selectedClass.grade} - Section {selectedClass.section}</p>
                  <div className="mt-2">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span className="font-medium">{selectedClass.currentStrength}/{selectedClass.capacity} students</span>
                      <span>({Math.round((selectedClass.currentStrength / selectedClass.capacity) * 100)}% full)</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">

                <div>
                  <Label className="text-sm font-medium text-gray-500">Academic Year</Label>
                  <p className="text-sm">{selectedClass.academicYear}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Class Teacher</Label>
                  <p className="text-sm">{selectedClass.classTeacherId || 'Not assigned'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Schedule</Label>
                  <p className="text-sm">{selectedClass.schedule?.startTime} - {selectedClass.schedule?.endTime}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-500">Subjects</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedClass.subjects?.map((subject, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {subject}
                    </Badge>
                  ))}
                </div>
              </div>

              {selectedClass.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-500">Description</Label>
                  <p className="text-sm">{selectedClass.description}</p>
                </div>
              )}

              <div className="flex justify-end pt-4 border-t">
                <Button onClick={() => setIsViewDialogOpen(false)} className="w-full sm:w-auto">
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Class Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the class
              {selectedClass && ` "${selectedClass.name}"`}
              and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedClass(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteClass}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Deleting...' : 'Delete Class'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
