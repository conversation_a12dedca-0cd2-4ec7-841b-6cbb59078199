import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  FileText,
  BarChart3,
  PieChart,
  TrendingUp,
  Users,
  GraduationCap,
  DollarSign,
  Calendar as CalendarIcon,
  Filter,
  Eye,
  Share,
  MoreVertical
} from "lucide-react";
import { format } from "date-fns";
import { useAuth } from "@/contexts/NewAuthContext";
import { MetricCard } from "@/components/MetricCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// Mock report data
const mockReports = [
  {
    id: '1',
    title: 'Monthly Attendance Report',
    type: 'attendance',
    description: 'Comprehensive attendance analysis for November 2024',
    generatedBy: 'Admin Office',
    createdAt: '2024-12-01',
    format: 'PDF',
    size: '2.3 MB',
    downloads: 15
  },
  {
    id: '2',
    title: 'Academic Performance Analysis',
    type: 'academic',
    description: 'Student performance analysis across all subjects',
    generatedBy: 'Ms. Davis',
    createdAt: '2024-11-28',
    format: 'Excel',
    size: '1.8 MB',
    downloads: 23
  },
  {
    id: '3',
    title: 'Fee Collection Summary',
    type: 'financial',
    description: 'Fee collection status and outstanding payments',
    generatedBy: 'Finance Office',
    createdAt: '2024-11-25',
    format: 'PDF',
    size: '1.2 MB',
    downloads: 8
  }
];

const reportTypes = ['attendance', 'academic', 'financial', 'custom'];
const formats = ['PDF', 'Excel', 'CSV'];
const classes = ['All Classes', '10-A', '10-B', '11-A', '11-B', '12-A', '12-B'];
const subjects = ['All Subjects', 'Mathematics', 'Physics', 'Chemistry', 'English', 'History'];

export const Reports: React.FC = () => {
  const { user } = useAuth();
  const [selectedType, setSelectedType] = useState<string>('all');
  const [reports, setReports] = useState(mockReports);
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [selectedClass, setSelectedClass] = useState<string>('all');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const isMobile = useIsMobile();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'attendance':
        return <Users className="h-4 w-4" />;
      case 'academic':
        return <GraduationCap className="h-4 w-4" />;
      case 'financial':
        return <DollarSign className="h-4 w-4" />;
      case 'custom':
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'attendance':
        return 'bg-blue-100 text-blue-800';
      case 'academic':
        return 'bg-green-100 text-green-800';
      case 'financial':
        return 'bg-yellow-100 text-yellow-800';
      case 'custom':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredReports = reports.filter(report => {
    return selectedType === 'all' || report.type === selectedType;
  });

  const generateReport = () => {
    // Mock report generation
    const newReport = {
      id: Date.now().toString(),
      title: 'Custom Report',
      type: 'custom',
      description: 'Generated custom report',
      generatedBy: user?.firstName + ' ' + user?.lastName || 'User',
      createdAt: new Date().toISOString().split('T')[0],
      format: 'PDF',
      size: '1.5 MB',
      downloads: 0
    };
    setReports(prev => [newReport, ...prev]);
  };

  return (
    <div className="space-y-4 md:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-1 text-xs sm:text-sm md:text-base">
            Generate and manage comprehensive school reports
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={generateReport} className="text-xs md:text-sm px-3 py-2 h-9">
            <FileText className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Generate Report</span>
            <span className="sm:hidden">Generate</span>
          </Button>
        </div>
      </div>

      {/* Report Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
        <MetricCard
          title="Total Reports"
          value={reports.length.toString()}
          icon={<FileText className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="This Month"
          value="12"
          icon={<TrendingUp className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Downloads"
          value="156"
          icon={<Download className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-purple-500"
        />
        <MetricCard
          title="Shared Reports"
          value="8"
          icon={<Share className="h-5 w-5 sm:h-6 sm:w-6" />}
          iconBg="bg-yellow-500"
        />
      </div>

      <Tabs defaultValue="generate" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 lg:w-[600px]">
          <TabsTrigger value="generate" className="text-xs sm:text-sm">
            {isMobile ? "Generate" : "Generate Reports"}
          </TabsTrigger>
          <TabsTrigger value="existing" className="text-xs sm:text-sm">
            {isMobile ? "Reports" : "Existing Reports"}
          </TabsTrigger>
          <TabsTrigger value="analytics" className="text-xs sm:text-sm">
            {isMobile ? "Analytics" : "Analytics Dashboard"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Generate New Report</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Create custom reports with specific parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm">Report Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="attendance">Attendance Report</SelectItem>
                        <SelectItem value="academic">Academic Performance</SelectItem>
                        <SelectItem value="financial">Financial Report</SelectItem>
                        <SelectItem value="custom">Custom Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm">Format</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                      <SelectContent>
                        {formats.map(format => (
                          <SelectItem key={format} value={format}>{format}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm">Class</Label>
                    <Select value={selectedClass} onValueChange={setSelectedClass}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select class" />
                      </SelectTrigger>
                      <SelectContent>
                        {classes.map(cls => (
                          <SelectItem key={cls} value={cls}>{cls}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm">Subject</Label>
                    <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map(subject => (
                          <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm">Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal text-sm">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, "PPP") : "Pick start date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm">End Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal text-sm">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, "PPP") : "Pick end date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Report Title</Label>
                  <Input placeholder="Enter custom report title" className="text-sm" />
                </div>

                <Button onClick={generateReport} className="w-full text-sm">
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="existing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Report Filters</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Filter reports by type or search
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="space-y-2 flex-1 sm:flex-initial">
                  <Label className="text-sm">Report Type</Label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {reportTypes.map(type => (
                        <SelectItem key={type} value={type} className="capitalize">{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Generated Reports</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                View and download previously generated reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 sm:space-y-4">
                {filteredReports.map((report) => (
                  <Card key={report.id}>
                    <CardContent className="p-3 sm:p-4">
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex items-start gap-3 flex-1 min-w-0">
                          <div className={`p-2 rounded-lg ${getTypeColor(report.type)} flex-shrink-0`}>
                            {getTypeIcon(report.type)}
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-sm sm:text-base truncate">{report.title}</h3>
                            <p className="text-xs sm:text-sm text-gray-600 mt-1">{report.description}</p>
                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mt-2">
                              <span>By: {report.generatedBy}</span>
                              <span>Size: {report.size}</span>
                              <span>Created: {new Date(report.createdAt).toLocaleDateString()}</span>
                              <span>Downloads: {report.downloads}</span>
                            </div>
                          </div>
                        </div>
                        {isMobile ? (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem>
                                <Eye className="h-4 w-4 mr-2" />
                                View Report
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="h-4 w-4 mr-2" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Share className="h-4 w-4 mr-2" />
                                Share Report
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            <Button size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Attendance Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm">Grade 10</span>
                      <span className="text-sm font-medium">94.5%</span>
                    </div>
                    <Progress value={94.5} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm">Grade 11</span>
                      <span className="text-sm font-medium">92.1%</span>
                    </div>
                    <Progress value={92.1} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm">Grade 12</span>
                      <span className="text-sm font-medium">96.8%</span>
                    </div>
                    <Progress value={96.8} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Academic Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">A Grades</span>
                    <span className="text-sm font-medium">35%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">B Grades</span>
                    <span className="text-sm font-medium">42%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">C Grades</span>
                    <span className="text-sm font-medium">18%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Below C</span>
                    <span className="text-sm font-medium">5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Fee Collection Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm">Collected</span>
                      <span className="text-sm font-medium">87.3%</span>
                    </div>
                    <Progress value={87.3} className="h-2" />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Total Fees</p>
                      <p className="font-semibold">$485,200</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Outstanding</p>
                      <p className="font-semibold text-red-600">$61,800</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Monthly Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Student Enrollment</span>
                    <span className="text-sm font-medium text-green-600">↑ 8%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Average Attendance</span>
                    <span className="text-sm font-medium text-green-600">↑ 2.1%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Academic Performance</span>
                    <span className="text-sm font-medium text-green-600">↑ 1.5%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Fee Collection</span>
                    <span className="text-sm font-medium text-red-600">↓ 3.2%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
