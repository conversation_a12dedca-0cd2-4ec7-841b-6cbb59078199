import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { studentsService, teachersService, classesService, getCurrentSchoolId } from "@/services/supabaseService";
import { 
  Users, 
  GraduationCap, 
  BookOpen, 
  Building2,
  TrendingUp,
  UserCheck,
  Calendar,
  BarChart3,
  Plus,
  Eye,
  ArrowRight,
  Loader2
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";

interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  activeStudents: number;
  activeTeachers: number;
  maleStudents: number;
  femaleStudents: number;
  isLoading: boolean;
}

export const AdminDashboard: React.FC = () => {
  const { profile } = useAuth();
  const navigate = useNavigate();
  const { schoolSlug } = useParams<{ schoolSlug: string }>();

  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    activeStudents: 0,
    activeTeachers: 0,
    maleStudents: 0,
    femaleStudents: 0,
    isLoading: true
  });

  // Load dashboard statistics
  const loadDashboardStats = async () => {
    if (!schoolSlug) return;

    try {
      setStats(prev => ({ ...prev, isLoading: true }));
      
      const schoolId = await getCurrentSchoolId(schoolSlug);
      if (!schoolId) return;

      // Fetch data in parallel
      const [studentsData, teachersData, classesData] = await Promise.all([
        studentsService.getAll(schoolId),
        teachersService.getAll(schoolId),
        classesService.getAll(schoolId)
      ]);

      // Calculate statistics
      const totalStudents = studentsData?.length || 0;
      const totalTeachers = teachersData?.length || 0;
      const totalClasses = classesData?.length || 0;
      
      const activeStudents = studentsData?.filter(s => s.is_active !== false)?.length || 0;
      const activeTeachers = teachersData?.filter(t => t.is_active !== false)?.length || 0;
      
      const maleStudents = studentsData?.filter(s => s.gender?.toLowerCase() === 'male')?.length || 0;
      const femaleStudents = studentsData?.filter(s => s.gender?.toLowerCase() === 'female')?.length || 0;

      setStats({
        totalStudents,
        totalTeachers,
        totalClasses,
        activeStudents,
        activeTeachers,
        maleStudents,
        femaleStudents,
        isLoading: false
      });

    } catch (error) {
      console.error('Failed to load dashboard stats:', error);
      setStats(prev => ({ ...prev, isLoading: false }));
    }
  };

  useEffect(() => {
    loadDashboardStats();
  }, [schoolSlug]);

  // Metric Card Component
  const MetricCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    color, 
    onClick 
  }: {
    title: string;
    value: string | number;
    subtitle: string;
    icon: any;
    color: string;
    onClick?: () => void;
  }) => (
    <Card 
      className={`${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
        <div className={`p-2 rounded-lg ${color}`}>
          <Icon className="h-4 w-4 text-white" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">
          {stats.isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : value}
        </div>
        <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              Admin Dashboard
            </h1>
            <p className="text-gray-600 mt-1">
              Welcome back, {profile?.first_name || 'Admin'}!
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Badge variant="outline" className="w-fit">
              <Building2 className="h-3 w-3 mr-1" />
              School Admin
            </Badge>
          </div>
        </div>

        {/* Quick Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <MetricCard
            title="Total Students"
            value={stats.totalStudents}
            subtitle={`${stats.activeStudents} active students`}
            icon={GraduationCap}
            color="bg-blue-500"
            onClick={() => navigate('/students')}
          />
          
          <MetricCard
            title="Total Teachers"
            value={stats.totalTeachers}
            subtitle={`${stats.activeTeachers} active teachers`}
            icon={Users}
            color="bg-green-500"
            onClick={() => navigate('/teachers')}
          />
          
          <MetricCard
            title="Classes"
            value={stats.totalClasses}
            subtitle="Active classes"
            icon={BookOpen}
            color="bg-purple-500"
            onClick={() => navigate('/classes')}
          />
          
          <MetricCard
            title="Student Ratio"
            value={stats.activeTeachers > 0 ? `${Math.round(stats.activeStudents / stats.activeTeachers)}:1` : 'N/A'}
            subtitle="Students per teacher"
            icon={BarChart3}
            color="bg-orange-500"
          />
        </div>

        {/* Detailed Analytics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <MetricCard
            title="Active Enrollment"
            value={`${Math.round((stats.activeStudents / Math.max(stats.totalStudents, 1)) * 100)}%`}
            subtitle={`${stats.activeStudents} of ${stats.totalStudents} students`}
            icon={TrendingUp}
            color="bg-emerald-500"
          />
          
          <MetricCard
            title="Gender Distribution"
            value={`${stats.maleStudents}M / ${stats.femaleStudents}F`}
            subtitle={stats.totalStudents > 0 ? `${Math.round((stats.maleStudents / stats.totalStudents) * 100)}% male` : 'No data'}
            icon={UserCheck}
            color="bg-pink-500"
          />
          
          <MetricCard
            title="System Status"
            value="Online"
            subtitle="All systems operational"
            icon={Calendar}
            color="bg-indigo-500"
          />
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => navigate('/students')}
              >
                <GraduationCap className="h-4 w-4 mr-2" />
                Add New Student
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => navigate('/teachers')}
              >
                <Users className="h-4 w-4 mr-2" />
                Add New Teacher
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => navigate('/classes')}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Manage Classes
                <ArrowRight className="h-4 w-4 ml-auto" />
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Dashboard loaded</p>
                  <p className="text-xs text-gray-500">System data refreshed</p>
                </div>
                <span className="text-xs text-gray-400">Now</span>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Database synchronized</p>
                  <p className="text-xs text-gray-500">All records updated</p>
                </div>
                <span className="text-xs text-gray-400">5m ago</span>
              </div>
              
              {!stats.isLoading && (
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Current statistics</p>
                    <p className="text-xs text-gray-500">
                      {stats.totalStudents} students, {stats.totalTeachers} teachers
                    </p>
                  </div>
                  <span className="text-xs text-gray-400">Live</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

      </div>
    </div>
  );
};
