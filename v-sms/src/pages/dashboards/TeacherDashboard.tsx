import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MetricCard } from "@/components/MetricCard";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  BookOpen,
  Calendar,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  GraduationCap,
  ClipboardList,
  MessageCircle,
  Loader2
} from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { teachersService, teacherClassesService, studentsService, assignmentsService, examinationsService, timetableService, getCurrentSchoolId } from "@/services/supabaseService";
import { useToast } from "@/components/ui/use-toast";

export const TeacherDashboard: React.FC = () => {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [teacherData, setTeacherData] = useState<any>(null);
  const [assignedClasses, setAssignedClasses] = useState<any[]>([]);
  const [myStudents, setMyStudents] = useState<any[]>([]);
  const [myAssignments, setMyAssignments] = useState<any[]>([]);
  const [myExaminations, setMyExaminations] = useState<any[]>([]);
  const [myTimetable, setMyTimetable] = useState<any[]>([]);
  const [schoolId, setSchoolId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load teacher's assigned classes and related data
  useEffect(() => {
    initializeTeacherData();
  }, [user]);

  const initializeTeacherData = async () => {
    if (!user?.email) return;

    try {
      // Get school ID from URL
      const pathParts = window.location.pathname.split('/');
      const schoolSlug = pathParts[1];
      const currentSchoolId = await getCurrentSchoolId(schoolSlug);

      if (currentSchoolId) {
        setSchoolId(currentSchoolId);
        await loadTeacherData(currentSchoolId, user.email);
      }
    } catch (error) {
      console.error('Error initializing teacher data:', error);
      toast({
        title: "Error",
        description: "Failed to load teacher data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadTeacherData = async (schoolId: string, teacherEmail: string) => {
    try {
      console.log('👨‍🏫 Loading teacher data for:', teacherEmail);

      // Find teacher record by email
      const allTeachers = await teachersService.getAll(schoolId);
      const teacher = allTeachers.find((t: any) => t.email === teacherEmail);

      if (teacher) {
        console.log('👨‍🏫 Teacher found:', teacher.first_name, teacher.last_name);
        setTeacherData(teacher);

        // Load teacher's assigned classes
        const classAssignments = await teacherClassesService.getByTeacher(schoolId, teacher.id);
        const classes = classAssignments.map(assignment => assignment.class);
        setAssignedClasses(classes);
        console.log('👨‍🏫 Assigned to', classes.length, 'classes');

        // Load students from assigned classes
        if (classes.length > 0) {
          const classIds = classes.map(cls => cls.id);
          await loadTeacherClassData(schoolId, classIds);
        }
      } else {
        console.log('👨‍🏫 Teacher record not found for email:', teacherEmail);
      }
    } catch (error) {
      console.error('Error loading teacher data:', error);
    }
  };

  const loadTeacherClassData = async (schoolId: string, classIds: string[]) => {
    try {
      // Load students from assigned classes only
      const allStudents = await studentsService.getAll(schoolId);
      const classStudents = allStudents.filter((s: any) => classIds.includes(s.class_id));
      setMyStudents(classStudents);
      console.log('👨‍🏫 Managing', classStudents.length, 'students');

      // Load assignments for assigned classes
      const allAssignments = await assignmentsService.getAll(schoolId);
      const classAssignments = allAssignments.filter((a: any) => classIds.includes(a.class_id));
      setMyAssignments(classAssignments);

      // Load examinations for assigned classes
      const allExaminations = await examinationsService.getAll(schoolId);
      const classExaminations = allExaminations.filter((e: any) => classIds.includes(e.class_id));
      setMyExaminations(classExaminations);

      // Load timetable for assigned classes
      const allTimetable = await Promise.all(
        classIds.map(classId => timetableService.getByClass(schoolId, classId))
      );
      const flatTimetable = allTimetable.flat();
      setMyTimetable(flatTimetable);
    } catch (error) {
      console.error('Error loading teacher class data:', error);
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'take-attendance':
        navigate('/attendance');
        break;
      case 'enter-grades':
        navigate('/grades');
        break;
      case 'create-assignment':
        navigate('/assignments');
        break;
      case 'message-parents':
        navigate('/communication');
        break;
      case 'new-assignment':
        navigate('/assignments');
        break;
      case 'grade-exams':
        navigate('/grades');
        break;
      case 'submit-plans':
        navigate('/timetable');
        break;
      case 'view-meetings':
        navigate('/communication');
        break;
      default:
        console.log('Unknown action:', action);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading teacher dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Good morning, {teacherData?.first_name || user?.firstName}!
          </h1>
          <p className="text-gray-600 mt-1">
            {assignedClasses.length > 0
              ? `Managing ${assignedClasses.length} classes with ${myStudents.length} students`
              : "Ready to inspire young minds today?"
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => handleQuickAction('new-assignment')}>
            <Plus className="h-4 w-4 mr-2" />
            New Assignment
          </Button>
        </div>
      </div>

      {/* Assigned Classes */}
      {assignedClasses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              My Assigned Classes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {assignedClasses.map((cls) => (
                <Badge key={cls.id} variant="outline" className="bg-blue-50 text-blue-700">
                  {cls.name} {cls.section}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="My Students"
          value={myStudents.length.toString()}
          change={`Across ${assignedClasses.length} classes`}
          changeType="neutral"
          icon={<Users className="h-6 w-6" />}
          iconBg="bg-blue-500"
        />
        <MetricCard
          title="My Classes"
          value={assignedClasses.length.toString()}
          change="Assigned to you"
          changeType="neutral"
          icon={<BookOpen className="h-6 w-6" />}
          iconBg="bg-green-500"
        />
        <MetricCard
          title="Assignments"
          value={myAssignments.length.toString()}
          change="Created by you"
          changeType="neutral"
          icon={<FileText className="h-6 w-6" />}
          iconBg="bg-yellow-500"
        />
        <MetricCard
          title="Attendance Rate"
          value="92.5%"
          change="↑ 2% this week"
          changeType="increase"
          icon={<CheckCircle className="h-6 w-6" />}
          iconBg="bg-emerald-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Today's Schedule
            </CardTitle>
            <CardDescription>
              Your classes and activities for today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold">
                  9:00
                </div>
                <div className="flex-1">
                  <p className="font-medium">Mathematics - Grade 10A</p>
                  <p className="text-sm text-gray-600">Room 201 • Algebra & Functions</p>
                </div>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Current</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 font-bold">
                  10:30
                </div>
                <div className="flex-1">
                  <p className="font-medium">Physics - Grade 11B</p>
                  <p className="text-sm text-gray-600">Lab 3 • Electromagnetic Waves</p>
                </div>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Next</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 font-bold">
                  2:00
                </div>
                <div className="flex-1">
                  <p className="font-medium">Mathematics - Grade 12A</p>
                  <p className="text-sm text-gray-600">Room 201 • Calculus Review</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for teachers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button
                className="h-20 bg-blue-500 hover:bg-blue-600 flex-col"
                onClick={() => handleQuickAction('take-attendance')}
              >
                <ClipboardList className="h-6 w-6 mb-2" />
                <span className="text-sm">Take Attendance</span>
              </Button>
              <Button
                className="h-20 bg-green-500 hover:bg-green-600 flex-col"
                onClick={() => handleQuickAction('enter-grades')}
              >
                <GraduationCap className="h-6 w-6 mb-2" />
                <span className="text-sm">Enter Grades</span>
              </Button>
              <Button
                className="h-20 bg-purple-500 hover:bg-purple-600 flex-col"
                onClick={() => handleQuickAction('create-assignment')}
              >
                <FileText className="h-6 w-6 mb-2" />
                <span className="text-sm">Create Assignment</span>
              </Button>
              <Button
                className="h-20 bg-yellow-500 hover:bg-yellow-600 flex-col"
                onClick={() => handleQuickAction('message-parents')}
              >
                <MessageCircle className="h-6 w-6 mb-2" />
                <span className="text-sm">Message Parents</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Assignments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Assignments</CardTitle>
            <CardDescription>
              Assignments you've created recently
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Quadratic Equations Worksheet</p>
                  <p className="text-sm text-gray-600">Grade 10A • Due: Dec 15</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">28/30 submitted</p>
                  <p className="text-xs text-gray-500">2 pending</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Wave Properties Lab Report</p>
                  <p className="text-sm text-gray-600">Grade 11B • Due: Dec 18</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">15/25 submitted</p>
                  <p className="text-xs text-gray-500">10 pending</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Calculus Problem Set 5</p>
                  <p className="text-sm text-gray-600">Grade 12A • Due: Dec 20</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">8/22 submitted</p>
                  <p className="text-xs text-gray-500">14 pending</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Pending Tasks
            </CardTitle>
            <CardDescription>
              Tasks that need your attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <Clock className="h-5 w-5 text-red-500" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-red-800">Grade Midterm Exams</p>
                  <p className="text-xs text-red-600">23 papers pending • Due: Tomorrow</p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickAction('grade-exams')}
                >
                  Grade Now
                </Button>
              </div>

              <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <FileText className="h-5 w-5 text-yellow-500" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-yellow-800">Submit Lesson Plans</p>
                  <p className="text-xs text-yellow-600">Next week's plans • Due: Friday</p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickAction('submit-plans')}
                >
                  Submit
                </Button>
              </div>

              <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <Users className="h-5 w-5 text-blue-500" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-800">Parent Meetings</p>
                  <p className="text-xs text-blue-600">3 meetings scheduled this week</p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickAction('view-meetings')}
                >
                  View Schedule
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
