import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  GraduationCap,
  Users,
  BookOpen,
  Calendar,
  BarChart3,
  Shield,
  Clock,
  Award,
  CheckCircle,
  ArrowRight,
  User,
  ClipboardList,
  FileText,
  DollarSign,
  MessageCircle,
  Bell,
  Settings,
  Smartphone,
  Cloud,
  Lock,
  Zap
} from 'lucide-react';
import { PublicHeader } from '@/components/PublicHeader';
import { PublicFooter } from '@/components/PublicFooter';

export const Features: React.FC = () => {
  const coreFeatures = [
    {
      icon: <Users className="h-8 w-8" />,
      title: "Student Management",
      description: "Comprehensive student profiles with academic records, attendance tracking, and performance analytics.",
      features: ["Student enrollment & registration", "Academic record management", "Attendance tracking", "Parent communication"]
    },
    {
      icon: <User className="h-8 w-8" />,
      title: "Teacher Portal",
      description: "Empower educators with tools for lesson planning, grading, and student communication.",
      features: ["Lesson planning tools", "Grade book management", "Assignment creation", "Student progress tracking"]
    },
    {
      icon: <BookOpen className="h-8 w-8" />,
      title: "Academic Management",
      description: "Manage courses, curricula, assignments, and academic calendars with ease.",
      features: ["Course management", "Curriculum planning", "Assignment distribution", "Academic calendar"]
    },
    {
      icon: <Calendar className="h-8 w-8" />,
      title: "Timetable & Scheduling",
      description: "Automated scheduling system for classes, exams, and school events.",
      features: ["Automated timetabling", "Exam scheduling", "Event management", "Resource allocation"]
    },
    {
      icon: <ClipboardList className="h-8 w-8" />,
      title: "Attendance Management",
      description: "Real-time attendance tracking with automated notifications and reporting.",
      features: ["Digital attendance", "Automated alerts", "Attendance reports", "Parent notifications"]
    },
    {
      icon: <BarChart3 className="h-8 w-8" />,
      title: "Analytics & Reports",
      description: "Detailed insights and reports on student performance and school operations.",
      features: ["Performance analytics", "Custom reports", "Data visualization", "Trend analysis"]
    }
  ];

  const additionalFeatures = [
    {
      icon: <DollarSign className="h-8 w-8" />,
      title: "Fee Management",
      description: "Streamlined fee collection and financial tracking",
      features: ["Online payments", "Fee tracking", "Financial reports", "Payment reminders"]
    },
    {
      icon: <MessageCircle className="h-8 w-8" />,
      title: "Communication Hub",
      description: "Seamless communication between all stakeholders",
      features: ["Messaging system", "Announcements", "Parent-teacher chat", "Broadcast notifications"]
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Examination System",
      description: "Complete examination and result management",
      features: ["Exam creation", "Result processing", "Grade calculation", "Report cards"]
    },
    {
      icon: <FileText className="h-8 w-8" />,
      title: "Document Management",
      description: "Centralized document storage and sharing",
      features: ["File storage", "Document sharing", "Version control", "Access permissions"]
    }
  ];

  const technicalFeatures = [
    {
      icon: <Cloud className="h-6 w-6" />,
      title: "Cloud-Based",
      description: "Access your data from anywhere, anytime with our secure cloud infrastructure."
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: "Mobile Responsive",
      description: "Fully optimized for mobile devices with native app-like experience."
    },
    {
      icon: <Lock className="h-6 w-6" />,
      title: "Enterprise Security",
      description: "Bank-level security with encryption, backups, and compliance standards."
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "High Performance",
      description: "Lightning-fast performance with 99.9% uptime guarantee."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <PublicHeader />

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Badge className="mb-6 bg-green-100 text-green-800 border-green-200">
              ⚡ Powerful Features
            </Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent"> Manage </span>
              Your School
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Comprehensive tools designed to streamline operations, enhance communication, 
              and improve educational outcomes for your entire school community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/login">
                <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg">
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/contact">
                <Button size="lg" variant="outline" className="border-gray-300 px-8 py-4 text-lg">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Core Features That Transform Schools
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Essential tools that every modern school needs to operate efficiently and effectively.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreFeatures.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center text-green-600 mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-600 text-base">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Advanced Capabilities
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Additional features that take your school management to the next level.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {additionalFeatures.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center text-blue-600 mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-600 text-sm">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-1">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-xs text-gray-600">
                        <CheckCircle className="h-3 w-3 text-blue-600 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Built for Modern Schools
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Enterprise-grade technology that scales with your institution.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {technicalFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center text-white mx-auto mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Feature Tabs */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Explore Features by Role
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how SchoolMS benefits different users in your educational community.
            </p>
          </div>
          <Tabs defaultValue="admin" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="admin">Administrators</TabsTrigger>
              <TabsTrigger value="teacher">Teachers</TabsTrigger>
              <TabsTrigger value="student">Students</TabsTrigger>
              <TabsTrigger value="parent">Parents</TabsTrigger>
            </TabsList>
            
            <TabsContent value="admin" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <Shield className="h-8 w-8 text-green-600 mb-4" />
                    <h3 className="font-semibold mb-2">Complete Control</h3>
                    <p className="text-sm text-gray-600">Manage all aspects of your school from a single dashboard with comprehensive administrative tools.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <BarChart3 className="h-8 w-8 text-green-600 mb-4" />
                    <h3 className="font-semibold mb-2">Data-Driven Insights</h3>
                    <p className="text-sm text-gray-600">Make informed decisions with detailed analytics and reports on all school operations.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <Users className="h-8 w-8 text-green-600 mb-4" />
                    <h3 className="font-semibold mb-2">User Management</h3>
                    <p className="text-sm text-gray-600">Easily manage users, roles, and permissions across your entire school system.</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="teacher" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <BookOpen className="h-8 w-8 text-blue-600 mb-4" />
                    <h3 className="font-semibold mb-2">Lesson Planning</h3>
                    <p className="text-sm text-gray-600">Create and organize lesson plans with integrated curriculum mapping and resource sharing.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <Award className="h-8 w-8 text-blue-600 mb-4" />
                    <h3 className="font-semibold mb-2">Grade Management</h3>
                    <p className="text-sm text-gray-600">Streamlined grading system with automated calculations and progress tracking.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <MessageCircle className="h-8 w-8 text-blue-600 mb-4" />
                    <h3 className="font-semibold mb-2">Parent Communication</h3>
                    <p className="text-sm text-gray-600">Direct communication channels with parents for updates and conferences.</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="student" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <Calendar className="h-8 w-8 text-purple-600 mb-4" />
                    <h3 className="font-semibold mb-2">Personal Dashboard</h3>
                    <p className="text-sm text-gray-600">View schedules, assignments, grades, and important announcements in one place.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <FileText className="h-8 w-8 text-purple-600 mb-4" />
                    <h3 className="font-semibold mb-2">Assignment Tracking</h3>
                    <p className="text-sm text-gray-600">Keep track of assignments, due dates, and submission status with automated reminders.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <BarChart3 className="h-8 w-8 text-purple-600 mb-4" />
                    <h3 className="font-semibold mb-2">Progress Monitoring</h3>
                    <p className="text-sm text-gray-600">Monitor academic progress with detailed grade reports and performance analytics.</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="parent" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <Bell className="h-8 w-8 text-orange-600 mb-4" />
                    <h3 className="font-semibold mb-2">Real-time Updates</h3>
                    <p className="text-sm text-gray-600">Receive instant notifications about attendance, grades, and school announcements.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <ClipboardList className="h-8 w-8 text-orange-600 mb-4" />
                    <h3 className="font-semibold mb-2">Attendance Monitoring</h3>
                    <p className="text-sm text-gray-600">Track your child's attendance with detailed reports and automated alerts.</p>
                  </CardContent>
                </Card>
                <Card className="border-0 shadow-lg">
                  <CardContent className="p-6">
                    <DollarSign className="h-8 w-8 text-orange-600 mb-4" />
                    <h3 className="font-semibold mb-2">Fee Management</h3>
                    <p className="text-sm text-gray-600">View and pay school fees online with detailed transaction history.</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your School?
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Join thousands of schools already using our platform to streamline operations and improve outcomes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/login">
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100 px-8 py-4 text-lg">
                Get Started
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
                <span className="text-lg font-bold">SchoolMS</span>
              </div>
              <p className="text-gray-400">
                Empowering educational institutions with modern management solutions.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/features" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/documentation" className="hover:text-white transition-colors">Documentation</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/about" className="hover:text-white transition-colors">About</a></li>
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/help-center" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact Us</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 SchoolMS. All rights reserved.</p>
          </div>
      <PublicFooter />
    </div>
  );
};
