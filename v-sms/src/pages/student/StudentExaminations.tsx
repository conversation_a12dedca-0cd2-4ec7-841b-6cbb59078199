import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Calendar, Clock, BookOpen, FileText, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

export const StudentExaminations = () => {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [examinations, setExaminations] = useState<any[]>([]);
  const [studentClass, setStudentClass] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string>('');

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadStudentExaminations(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadStudentExaminations = async (schoolId: string) => {
    try {
      console.log('📝 Loading student examinations for profile:', profile?.id);

      // Find student by profile_id
      const { data: students } = await supabase
        .from('students')
        .select('*, class:classes(id, name, section)')
        .eq('school_id', schoolId)
        .eq('profile_id', profile?.id)
        .eq('is_active', true);

      if (!students || students.length === 0) {
        console.log('📝 No student found for profile:', profile?.id);
        return;
      }

      const student = students[0];
      setStudentClass(student.class);
      console.log('📝 Student found:', student.first_name, student.last_name, 'Class:', student.class?.name);

      if (student.class_id) {
        // Load examinations for student's class through exam_subjects
        const { data: examSubjectsData } = await supabase
          .from('exam_subjects')
          .select(`
            *,
            examination:examinations(
              id,
              name,
              description,
              exam_type,
              start_date,
              end_date,
              is_active
            ),
            subject:subjects(id, name, code)
          `)
          .eq('school_id', schoolId)
          .eq('class_id', student.class_id)
          .order('exam_date', { ascending: true });

        // Extract unique examinations from exam_subjects
        const uniqueExams = new Map();
        examSubjectsData?.forEach(examSubject => {
          if (examSubject.examination && examSubject.examination.is_active) {
            const exam = examSubject.examination;
            if (!uniqueExams.has(exam.id)) {
              uniqueExams.set(exam.id, {
                ...exam,
                subjects: []
              });
            }
            uniqueExams.get(exam.id).subjects.push({
              ...examSubject.subject,
              exam_date: examSubject.exam_date,
              start_time: examSubject.start_time,
              end_time: examSubject.end_time,
              max_marks: examSubject.max_marks,
              room_number: examSubject.room_number
            });
          }
        });

        const examsArray = Array.from(uniqueExams.values());
        setExaminations(examsArray);
        console.log('📝 Loaded', examsArray.length, 'class-specific examinations');
      }
    } catch (error) {
      console.error('Error loading student examinations:', error);
    }
  };

  const getExamStatus = (startDate: string, endDate: string) => {
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate || startDate);

    if (now < start) {
      return { status: 'upcoming', color: 'bg-blue-100 text-blue-800' };
    } else if (now >= start && now <= end) {
      return { status: 'ongoing', color: 'bg-green-100 text-green-800' };
    } else {
      return { status: 'completed', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading your examinations...</span>
      </div>
    );
  }

  if (!studentClass) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Class Assigned</h3>
            <p className="text-gray-600">You haven't been assigned to a class yet. Please contact your school administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Examinations</h1>
        <p className="text-gray-600">Upcoming and past examinations for {studentClass.name} {studentClass.section}</p>
      </div>

      {/* Class Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Class Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            {studentClass.name} {studentClass.section}
          </Badge>
        </CardContent>
      </Card>

      {/* Examinations List */}
      <div className="space-y-4">
        {examinations.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No Examinations</h3>
              <p className="text-gray-600">No examinations have been scheduled yet.</p>
            </CardContent>
          </Card>
        ) : (
          examinations.map((exam) => {
            const { status, color } = getExamStatus(exam.start_date, exam.end_date);
            
            return (
              <Card key={exam.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        {exam.name}
                      </CardTitle>
                      {exam.description && (
                        <CardDescription className="mt-1">
                          {exam.description}
                        </CardDescription>
                      )}
                    </div>
                    <Badge className={color}>
                      {status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Start Date</p>
                        <p className="text-sm text-gray-600">{formatDate(exam.start_date)}</p>
                      </div>
                    </div>
                    {exam.end_date && exam.end_date !== exam.start_date && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">End Date</p>
                          <p className="text-sm text-gray-600">{formatDate(exam.end_date)}</p>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Type</p>
                        <p className="text-sm text-gray-600">{exam.exam_type || 'Regular'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Academic Year</p>
                        <p className="text-sm text-gray-600">{exam.academic_year || '2024-25'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Subjects for this exam */}
                  {exam.subjects && exam.subjects.length > 0 && (
                    <div className="mt-4 pt-4 border-t">
                      <h4 className="font-medium mb-2">Subjects:</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {exam.subjects.map((subject, index) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <div>
                              <span className="font-medium">{subject.name}</span>
                              {subject.code && (
                                <span className="text-sm text-gray-500 ml-2">({subject.code})</span>
                              )}
                            </div>
                            <div className="text-sm text-gray-600">
                              {subject.exam_date && new Date(subject.exam_date).toLocaleDateString()}
                              {subject.start_time && ` ${subject.start_time}`}
                              {subject.max_marks && ` • ${subject.max_marks} marks`}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};
