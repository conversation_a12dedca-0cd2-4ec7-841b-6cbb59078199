import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Clock, User, MapPin, BookOpen, Loader2, Plus } from "lucide-react";
import { useAuth } from "@/contexts/NewAuthContext";
import { supabase } from '@/integrations/supabase/client';
import { getCurrentSchoolId } from "@/services/supabaseService";

const days = [
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
];

export const TeacherTimetable = () => {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [timetable, setTimetable] = useState<any[]>([]);
  const [assignedClasses, setAssignedClasses] = useState<any[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [teacherInfo, setTeacherInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [schoolId, setSchoolId] = useState<string>('');

  useEffect(() => {
    initializeData();
  }, []);

  useEffect(() => {
    if (selectedClass && schoolId) {
      loadClassTimetable(schoolId, selectedClass);
    }
  }, [selectedClass, schoolId]);

  const initializeData = async () => {
    try {
      const schoolSlug = window.location.pathname.split('/')[1];
      const id = await getCurrentSchoolId(schoolSlug);
      if (!id) {
        toast({
          title: "Error",
          description: "School not found",
          variant: "destructive",
        });
        return;
      }
      setSchoolId(id);
      await loadTeacherData(id);
    } catch (error) {
      console.error('Error initializing:', error);
      toast({
        title: "Error",
        description: "Failed to initialize page",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadTeacherData = async (schoolId: string) => {
    try {
      console.log('👨‍🏫 Loading teacher data for:', user?.email);
      
      // Find teacher by email
      const { data: teachers } = await supabase
        .from('teachers')
        .select('*')
        .eq('school_id', schoolId)
        .eq('email', user?.email)
        .eq('is_active', true);

      if (!teachers || teachers.length === 0) {
        console.log('👨‍🏫 No teacher found for email:', user?.email);
        return;
      }

      const teacher = teachers[0];
      setTeacherInfo(teacher);
      console.log('👨‍🏫 Teacher found:', teacher.first_name, teacher.last_name);

      // Load teacher's assigned classes
      const { data: classAssignments } = await supabase
        .from('teacher_classes')
        .select(`
          *,
          class:classes(id, name, section, grade_level)
        `)
        .eq('school_id', schoolId)
        .eq('teacher_id', teacher.id)
        .eq('is_active', true);

      const classes = classAssignments?.map(assignment => assignment.class) || [];
      setAssignedClasses(classes);
      console.log('👨‍🏫 Assigned to', classes.length, 'classes');

      // Set first class as default
      if (classes.length > 0) {
        setSelectedClass(classes[0].id);
      }
    } catch (error) {
      console.error('Error loading teacher data:', error);
    }
  };

  const loadClassTimetable = async (schoolId: string, classId: string) => {
    try {
      console.log('📅 Loading timetable for class:', classId);
      
      const { data: timetableData } = await supabase
        .from('timetable')
        .select(`
          *,
          subject:subjects(id, name, code),
          teacher:teachers(id, first_name, last_name)
        `)
        .eq('school_id', schoolId)
        .eq('class_id', classId)
        .eq('is_active', true)
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true });

      setTimetable(timetableData || []);
      console.log('📅 Loaded', timetableData?.length || 0, 'timetable entries');
    } catch (error) {
      console.error('Error loading class timetable:', error);
    }
  };

  const groupedTimetable = timetable.reduce((acc, item) => {
    const day = days.find(d => d.value === item.day_of_week);
    const dayName = day ? day.label : 'Unknown';
    if (!acc[dayName]) acc[dayName] = [];
    acc[dayName].push(item);
    return acc;
  }, {} as Record<string, any[]>);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading timetable...</span>
      </div>
    );
  }

  if (!teacherInfo) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Teacher Not Found</h3>
            <p className="text-gray-600">No teacher record found for your email. Please contact the administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (assignedClasses.length === 0) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Classes Assigned</h3>
            <p className="text-gray-600">You haven't been assigned to any classes yet. Please contact the administrator.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Class Timetables</h1>
        <p className="text-gray-600">Manage timetables for your assigned classes</p>
      </div>

      {/* Teacher Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Teacher Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Name</p>
              <p className="text-lg">{teacherInfo.first_name} {teacherInfo.last_name}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Assigned Classes</p>
              <div className="flex flex-wrap gap-2 mt-1">
                {assignedClasses.map((cls) => (
                  <Badge key={cls.id} variant="outline" className="bg-blue-50 text-blue-700">
                    {cls.name} {cls.section}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Class Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Select Class</CardTitle>
          <CardDescription>Choose a class to view its timetable</CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select class" />
            </SelectTrigger>
            <SelectContent>
              {assignedClasses.map(cls => (
                <SelectItem key={cls.id} value={cls.id}>
                  {cls.name} {cls.section}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Timetable Display */}
      {selectedClass && (
        <div className="grid gap-6">
          {days.map(day => (
            <Card key={day.value}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  {day.label}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {groupedTimetable[day.label] && groupedTimetable[day.label].length > 0 ? (
                  <div className="space-y-3">
                    {groupedTimetable[day.label].map((period) => (
                      <div key={period.id} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <BookOpen className="h-4 w-4 text-blue-600" />
                              <span className="font-medium">{period.subject?.name || 'Unknown Subject'}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <User className="h-4 w-4" />
                              <span>
                                {period.teacher 
                                  ? `${period.teacher.first_name} ${period.teacher.last_name}`
                                  : 'Unknown Teacher'
                                }
                              </span>
                            </div>
                            {period.room_number && (
                              <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin className="h-4 w-4" />
                                <span>{period.room_number}</span>
                              </div>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="font-medium">{period.start_time} - {period.end_time}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-4">No periods scheduled</p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
