
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface School {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  settings: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface SchoolContextType {
  currentSchool: School | null;
  setCurrentSchool: (school: School | null) => void;
  schoolSlug: string | null;
  setSchoolSlug: (slug: string | null) => void;
  loading: boolean;
  loadSchoolBySlug: (slug: string) => Promise<School | null>;
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined);

export const SchoolProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentSchool, setCurrentSchool] = useState<School | null>(null);
  const [schoolSlug, setSchoolSlug] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const loadSchoolBySlug = async (slug: string): Promise<School | null> => {
    console.log('🏫 Loading school by slug:', slug);
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('slug', slug)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error loading school:', error);
        return null;
      }

      const school = data as School;
      setCurrentSchool(school);
      setSchoolSlug(slug);
      return school;
    } catch (error) {
      console.error('Error loading school:', error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return (
    <SchoolContext.Provider value={{
      currentSchool,
      setCurrentSchool,
      schoolSlug,
      setSchoolSlug,
      loading,
      loadSchoolBySlug
    }}>
      {children}
    </SchoolContext.Provider>
  );
};

export const useSchool = () => {
  const context = useContext(SchoolContext);
  if (context === undefined) {
    throw new Error('useSchool must be used within a SchoolProvider');
  }
  return context;
};
