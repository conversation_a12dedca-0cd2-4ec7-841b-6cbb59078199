
@tailwind base;
@tailwind components;
@tailwind utilities;

/* School Management System Design System */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 120 100% 20%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 120 100% 20%;

    --radius: 0.5rem;

    --sidebar-background: 120 100% 20%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 120 100% 20%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 120 100% 25%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 120 100% 30%;
    --sidebar-ring: 120 100% 20%;

    /* Custom school colors */
    --school-primary: 120 100% 20%;
    --school-secondary: 120 100% 25%;
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --info: 199 89% 48%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 120 100% 25%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 120 100% 25%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .gradient-header {
    background: linear-gradient(135deg, hsl(var(--school-primary)) 0%, hsl(var(--school-secondary)) 100%);
  }

  .stat-card {
    @apply bg-card rounded-lg border p-4 md:p-6 shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .metric-icon {
    @apply w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-white;
  }

  /* Mobile-specific improvements */
  @media (max-width: 768px) {
    .table-responsive {
      font-size: 0.875rem;
    }

    .mobile-card {
      @apply p-3 rounded-lg border bg-white shadow-sm;
    }

    .mobile-hidden {
      @apply hidden;
    }

    .mobile-compact {
      @apply text-xs leading-tight;
    }
  }

  /* Ensure dialogs are mobile-friendly */
  .dialog-mobile {
    @apply max-h-[85vh] overflow-y-auto;
  }
}
