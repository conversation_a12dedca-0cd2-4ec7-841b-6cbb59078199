import { useAuth } from '@/contexts/NewAuthContext';

interface DataAccessConfig {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canExport: boolean;
  canManage: boolean;
  viewMode: 'full' | 'limited' | 'own' | 'none';
}

// Role-based access configurations
const roleAccessConfigs = {
  admin: {
    students: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    teachers: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    parents: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    classes: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    subjects: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    departments: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    assignments: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    examinations: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    results: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    grades: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    attendance: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    timetable: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    fees: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    communications: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    notifications: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    reports: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const },
    settings: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'full' as const }
  },
  teacher: {
    students: { canView: true, canCreate: false, canEdit: true, canDelete: false, canExport: true, canManage: false, viewMode: 'limited' as const },
    teachers: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    parents: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    classes: { canView: true, canCreate: false, canEdit: true, canDelete: false, canExport: true, canManage: false, viewMode: 'limited' as const },
    subjects: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    departments: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    assignments: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'limited' as const },
    examinations: { canView: true, canCreate: true, canEdit: true, canDelete: true, canExport: true, canManage: true, viewMode: 'limited' as const },
    results: { canView: true, canCreate: true, canEdit: true, canDelete: false, canExport: true, canManage: true, viewMode: 'limited' as const },
    grades: { canView: true, canCreate: true, canEdit: true, canDelete: false, canExport: true, canManage: true, viewMode: 'limited' as const },
    attendance: { canView: true, canCreate: true, canEdit: true, canDelete: false, canExport: true, canManage: true, viewMode: 'limited' as const },
    timetable: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: true, canManage: false, viewMode: 'limited' as const },
    fees: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    communications: { canView: true, canCreate: true, canEdit: true, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    notifications: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    reports: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    settings: { canView: true, canCreate: false, canEdit: true, canDelete: false, canExport: false, canManage: false, viewMode: 'own' as const }
  },

  parent: {
    students: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    teachers: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    parents: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    classes: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    subjects: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    departments: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    assignments: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    examinations: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    results: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    grades: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    attendance: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    timetable: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    fees: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: true, canManage: true, viewMode: 'limited' as const },
    communications: { canView: true, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'limited' as const },
    notifications: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    reports: { canView: false, canCreate: false, canEdit: false, canDelete: false, canExport: false, canManage: false, viewMode: 'none' as const },
    settings: { canView: true, canCreate: false, canEdit: true, canDelete: false, canExport: false, canManage: false, viewMode: 'own' as const }
  }
};

export const useDataAccess = (page?: string) => {
  const { user, profile } = useAuth();

  const getPageAccess = (pageName: string): DataAccessConfig => {
    const userRole = profile?.role || 'student';
    const roleConfig = roleAccessConfigs[userRole as keyof typeof roleAccessConfigs];
    return roleConfig[pageName as keyof typeof roleConfig] || {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      canExport: false,
      canManage: false,
      viewMode: 'none' as const
    };
  };

  const currentAccess = page ? getPageAccess(page) : getPageAccess('default');

  const hasAccess = (action: keyof DataAccessConfig, pageName?: string): boolean => {
    const access = pageName ? getPageAccess(pageName) : currentAccess;
    return access[action] as boolean;
  };

  return {
    hasAccess,
    getPageAccess,
    currentAccess,
    canView: () => currentAccess.canView,
    canCreate: () => currentAccess.canCreate,
    canEdit: () => currentAccess.canEdit,
    canDelete: () => currentAccess.canDelete,
    canExport: () => currentAccess.canExport,
    canManage: () => currentAccess.canManage,
    getViewMode: () => currentAccess.viewMode
  };
};
