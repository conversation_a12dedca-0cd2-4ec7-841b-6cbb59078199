
// Core types for the School Management System

export type UserRole = 'admin' | 'teacher' | 'parent' | 'student';

// Multi-tenant School/Organization types
export interface School {
  id: string;
  name: string;
  slug: string; // Unique identifier for subdomain/URL
  domain?: string; // Custom domain if available
  logo?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  establishedYear?: number;
  principalName?: string;
  totalStudents?: number;
  totalTeachers?: number;
  settings: {
    academicYearStart: string;
    academicYearEnd: string;
    currency: string;
    timezone: string;
    language: string;
    theme: {
      primaryColor: string;
      secondaryColor: string;
      logo?: string;
    };
    features: {
      enableOnlinePayments: boolean;
      enableParentPortal: boolean;
      enableStudentPortal: boolean;
      enableAttendanceTracking: boolean;
      enableGradeBook: boolean;
      enableLibraryManagement: boolean;
      enableTransportManagement: boolean;
      enableHostelManagement: boolean;
    };
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Admin extends User {
  role: 'admin';
  permissions: string[];
  isSuperAdmin?: boolean; // For platform-wide admin access
}

export interface Teacher extends User {
  role: 'teacher';
  employeeId: string;
  department: string;
  subjects: string[];
  classes: string[];
  qualifications: string[];
  experience: number;
  salary?: number;
  joiningDate: string;
}

export interface Student extends User {
  role: 'student';
  studentId: string;
  class: string;
  section: string;
  rollNumber: string;
  parentId: string;
  admissionDate: string;
  bloodGroup?: string;
  emergencyContact: string;
  medicalInfo?: string;
}

export interface Parent extends User {
  role: 'parent';
  children: string[]; // Student IDs
  occupation?: string;
  relationship: 'father' | 'mother' | 'guardian';
}

export interface Class {
  id: string;
  name: string;
  section: string;
  grade: number;
  teacherId: string;
  subjects: string[];
  students: string[];
  capacity: number;
  currentStrength: number;
  room: string;
  academicYear: string;
  classTeacherId: string;
  description?: string;
  schedule: {
    startTime: string;
    endTime: string;
  };
  createdAt: string;
}

export interface Subject {
  id: string;
  name: string;
  code: string;
  description: string;
  department: string;
  credits: number;
  duration: string;
  level: string;
  prerequisites: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Department {
  id: string;
  name: string;
  code: string;
  description: string;
  headOfDepartment: string;
  totalSubjects: number;
  totalTeachers: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Result {
  id: string;
  studentId: string;
  examId: string;
  subject: string;
  marksObtained: number;
  totalMarks: number;
  grade?: string;
  percentage: number;
  examDate: string;
  remarks?: string;
  createdAt: string;
}

export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  markedBy: string;
  notes?: string;
  createdAt: string;
}

export interface Grade {
  id: string;
  studentId: string;
  subjectId: string;
  classId: string;
  type: 'assignment' | 'quiz' | 'exam' | 'project';
  title: string;
  score: number;
  maxScore: number;
  percentage: number;
  grade: string;
  teacherId: string;
  date: string;
  feedback?: string;
  createdAt: string;
}

export interface Assignment {
  id: string;
  title: string;
  description: string;
  subjectId: string;
  classId: string;
  teacherId: string;
  dueDate: string;
  maxScore: number;
  instructions?: string;
  attachments?: string[];
  status: 'draft' | 'published' | 'closed';
  submissions: number;
  totalStudents: number;
  subject: string;
  class: string;
  createdAt: string;
}

export interface AssignmentSubmission {
  id: string;
  assignmentId: string;
  studentId: string;
  content: string;
  attachments?: string[];
  submittedAt: string;
  status: 'submitted' | 'late' | 'graded';
  score?: number;
  feedback?: string;
  gradedBy?: string;
  gradedAt?: string;
}

export interface Timetable {
  id: string;
  classId: string;
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday';
  periods: TimetablePeriod[];
  createdAt: string;
}

export interface TimetablePeriod {
  id: string;
  startTime: string;
  endTime: string;
  subjectId: string;
  teacherId: string;
  room: string;
  type: 'regular' | 'break' | 'lunch' | 'assembly';
}

export interface Fee {
  id: string;
  studentId: string;
  type: 'tuition' | 'transport' | 'library' | 'lab' | 'sports' | 'other';
  amount: number;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  paidAmount?: number;
  paidDate?: string;
  paymentMethod?: string;
  transactionId?: string;
  description?: string;
  createdAt: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  recipients: string[]; // User IDs
  senderId: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  expiresAt?: string;
}

export interface Exam {
  id: string;
  title: string;
  subjectId: string;
  classId: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: number; // in minutes
  maxMarks: number;
  instructions?: string;
  room: string;
  supervisorId: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  createdAt: string;
}

// School Registration and Onboarding Types
export interface SchoolRegistration {
  schoolName: string;
  adminFirstName: string;
  adminLastName: string;
  adminEmail: string;
  adminPhone: string;
  schoolAddress: string;
  schoolPhone: string;
  schoolEmail: string;
  estimatedStudents: number;
  estimatedTeachers: number;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  order: number;
}

export interface SchoolOnboarding {
  schoolId: string;
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  completedAt?: string;
  isCompleted: boolean;
}

export interface Report {
  id: string;
  title: string;
  type: 'attendance' | 'academic' | 'financial' | 'custom';
  generatedBy: string;
  data: any;
  filters: any;
  createdAt: string;
  format: 'pdf' | 'excel' | 'csv';
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface PasswordResetForm {
  email: string;
}

export interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Dashboard statistics
export interface DashboardStats {
  totalStudents: number;
  totalTeachers: number;
  totalClasses: number;
  totalSubjects: number;
  attendanceRate: number;
  feeCollectionRate: number;
  upcomingExams: number;
  pendingAssignments: number;
}

// Filter and pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  class?: string;
  subject?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}
